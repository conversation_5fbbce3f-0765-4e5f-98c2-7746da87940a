@import "tailwindcss" theme(static);

@import 'swiper/css';
@import 'swiper/css/pagination';
@import 'swiper/css/navigation';
@import 'swiper/css/a11y';

@source "../views/";
@source "../../app/";

@theme {
  --breakpoint-*: initial;
  --breakpoint-sm: 40rem; /* 640px */
  --breakpoint-md: 48rem; /* 768px */
  --breakpoint-lg: 75rem; /* 1200px */

  --font-body: 'Poppins', sans-serif;
  --font-headings: 'Funnel Display', sans-serif;

  --color-navy-blue: #223558;
  --color-light-blue: #E0EEF2;
  --color-yellow: #FFBB00;
  --color-neon-green: #7AFFD0;

  --radius-sm: 0.3125rem; /* 5px */
  --radius: 0.625rem; /* 10px */
  --radius-lg: 1.25rem; /* 20px */
}

:root {
  --header-height: 5.8125rem;
  --header-height-with-menu: 10.6875rem;
}

html {
  font-size: 16px;
  max-width: 100vw;
  max-width: 100dvw;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  overflow-y: auto;
  font-family: var(--font-body), sans-serif;
  line-height: 1.5;
  color: var(--color-navy-blue);
}

body.is-pro-section {
  --menu-primary-color: var(--color-navy-blue);
  --menu-secondary-color: var(--color-neon-green);
}

body.is-consumers-section {
  --menu-primary-color: var(--color-neon-green);
  --menu-secondary-color: var(--color-navy-blue);
}

body.is-primary-menu-active,
body.primary-sub-menu-is-open {
  overflow-y: hidden;
}

a {
  text-decoration: none;
  cursor: pointer;
}

button {
  cursor: pointer;
}

.wysiwyg h2 {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  font-family: var(--font-headings), sans-serif;
  font-size: theme('fontSize.2xl');
  font-weight: 700;
}

.wysiwyg h2:first-child {
  margin-top: 0;
}

.wysiwyg h2:last-child {
  margin-bottom: 0;
}

.wysiwyg h3 {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  font-size: theme('fontSize.xl');
  font-weight: 700;
}

.wysiwyg h3:first-child {
  margin-top: 0;
}

.wysiwyg h3:last-child {
  margin-bottom: 0;
}

.wysiwyg p {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.wysiwyg strong {
  font-weight: 500;
}

.wysiwyg p:first-child {
  margin-top: 0;
}

.wysiwyg p:last-child {
  margin-bottom: 0;
}

.wysiwyg a {
  text-decoration: underline;
}

.wysiwyg ul {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  list-style: none;
}

.wysiwyg ul li:not(:last-child) {
  margin-bottom: 1rem;
}

.wysiwyg ul li:before {
  font-family: "Font Awesome 6 Pro", sans-serif;
  font-weight: 900;
  content: '\f0da';
  margin-right: 0.5rem;
  color: var(--color-neon-green);
}

.wysiwyg ul:first-child {
  margin-top: 0;
}

.wysiwyg ul:last-child {
  margin-bottom: 0;
}

.wysiwyg table {
  width: 100%;
}

.wysiwyg table tbody {
  font-size: 1.125rem;
}
.wysiwyg table td:last-child {
  text-align: right;
}

.wysiwyg table td {
  padding-left: 4.875rem;
  padding-right: 4.875rem;
}

.wysiwyg table td:not(:last-child) {
  border-right: 2px solid rgba(34, 53, 88, .1);
}

.wysiwyg table tbody tr:nth-child(odd) td {
  background-color: #E0EEF2;
}

.wysiwyg table tbody tr:nth-child(even) td {
  background-color: #F3FCFF;
}
.wysiwyg table tbody td {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.wysiwyg table td:first-child {
  padding-left: 2.625rem;
  border-radius: 0.625rem 0 0 0.625rem;
}

.wysiwyg table td:last-child {
  padding-right: 1.875rem;
  border-radius: 0 0.625rem 0.625rem 0;
}

.wysiwyg table tbody i[class^="fa"] {
  font-size: 1.5rem;
  position: relative;
  top: 2px;
}

.wysiwyg table thead td {
  padding-bottom: 0.625rem;
  font-weight: bold;
}

#primary-menu {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  font-weight: 600;
  color: var(--menu-secondary-color);
  @variant lg {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 0 0.5rem;
    font-size: 0.75rem;
  }
}

#primary-menu > li {
  position: relative;
  @variant lg {
    position: static;
  }
}

#primary-menu > li > a {
  @variant lg {
    display: block;
    padding: 0.375rem 0.875rem;
    font-weight: var(--font-weight-medium);
    border: 2px solid var(--menu-primary-color);
    border-radius: var(--radius-sm);
    transition: border-color .2s linear;
  }
}

#primary-menu > li > a:hover {
  color: var(--color-yellow);
}

#primary-menu > li > a:focus-within,
#primary-menu > li.active > a {
  @variant lg {
    border-color: var(--color-yellow);
    outline: none;
  }
}

#primary-menu > li > ul > li > a:hover {
  color: var(--color-yellow);
}

#primary-menu > li > ul > li > button:focus-within,
#primary-menu > li > ul > li > a:focus-within {
  outline-width: 3px;
  outline-color: var(--color-yellow);
  outline-offset: 3px;
}

#primary-menu > li.active > a {
  @variant lg {
    border-color: var(--menu-secondary-color);
    font-weight: var(--font-weight-bold);
  }
}

#primary-menu > li.active:before {
  @variant lg {
    z-index: 0;
    content: '';
    position: absolute;
    top: var(--header-height-with-menu);
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--menu-primary-color);
    opacity: .75;
    backdrop-filter: blur(1.5625rem);
  }
}

#primary-menu > li > a {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  transition: color .2s linear;
  @variant lg {
    display: block;
    gap: 0;
  }
}

#primary-menu > li > a:before {
  content: '\f061';
  display: block;
  width: 0.875rem;
  font-family: "Font Awesome 6 Pro", sans-serif;
  font-weight: 400;
  opacity: 0;
  @variant lg {
    display: none;
  }
}

body.is-primary-menu-active #primary-menu-wrapper {
  translate: 0;
}

body.is-primary-menu-active #primary-menu-button {
  color: var(--menu-secondary-color);
}

#primary-menu > li.current-menu-ancestor > a:before {
  opacity: 1;
}

#primary-menu > li > ul {
  display: none;
  font-size: 0.875rem;
  font-weight: 400;
  flex-direction: column;
  gap: theme('spacing.5');
  padding-top: 1.25rem;
  padding-left: 2.25rem;
  @variant md {
    grid-template-columns: 1fr 1fr;
  }
  @variant lg {
    opacity: 0;
    position: absolute;
    top: var(--header-height-with-menu);
    right: 0;
    left: 0;
    display: none;
    grid-template-columns: 15.625rem repeat(2, 18.125rem);
    grid-template-rows: repeat(1000, auto);
    gap: 10px 24px;
    padding: 3.375rem 0;
    font-size: 1rem;
    transition: opacity .2s .2s linear;
    @apply container mx-auto;
  }
}

#primary-menu > li > ul:before {
  @variant lg {
    content: 'Banking services in Luxembourg';
    position: relative;
    top: -0.625rem;
    display: block;
    grid-column: 1/2;
    grid-row: 1/-1;
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    text-wrap: balance;
  }
}

#primary-menu > li > ul > li.menu-item-close-button {
  display: none;
  content: '\f057';
  font-family: "Font Awesome 6 Pro", sans-serif;
  position: absolute;
  top: 3.375rem;
  right: 0;
  font-size: 1.25rem;
  cursor: pointer;
  @variant lg {
    display: block;
  }
}

#primary-menu > li.active > ul {
  display: grid;
}

#primary-menu > li.active > ul {
  @variant lg {
    opacity: 1;
    display: grid;
  }
}

#primary-menu > li > ul > li {
  @variant lg {
    z-index: 1;
    position: relative;
    font-size: 0.75rem;
  }
}

#primary-menu > li > ul > li > a {
  display: grid;
  align-items: center;
  grid-template-columns: 1rem 1rem 1fr;
  gap: 1.25rem;
  transition: color .2s linear;
  @variant lg {
    grid-template-columns: 1rem 1fr;
  }
}

/** Arrow that indicates the current page */
#primary-menu > li > ul > li > a:before {
  content: '';
  font-family: "Font Awesome 6 Pro", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  justify-self: center;
  @variant lg {
    display: none;
  }
}

/** Context icon */
#primary-menu > li > ul > li > a > i {
  justify-self: center;
}

#primary-menu > li > ul > li.current-menu-item > a:before {
  content: '\f061';
}

#sub-menu-overlay {
  transition: opacity .3s linear, scale .3s ease-out;
}

body.primary-sub-menu-is-open #sub-menu-overlay {
  scale: 1;
  opacity: 1;
}

#footer-legal-menu {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.8');
  @variant lg {
    gap: theme('spacing.20');
  }
}

#footer-socials-menu {
  display: flex;
  gap: theme('spacing.4');
  font-size: 1.375rem; /* 22px */
  color: theme('color.neon-green');
}

#footer-quick-access-menu {
  list-style: disc inside;
  font-size: 0.875rem;
  line-height: 2.5rem;
  @variant md {
    line-height: normal;
  }
}

.member-category-button {
  padding: theme('spacing.2') theme('spacing.12');
  border: 1px solid theme('colors.gray.300');
  border-right-width: 0;
  background-color: theme('colors.gray.200');
  cursor: pointer;
}

li:last-child .member-category-button {
  border-right-width: 1px;
}

.member-category-button.active {
  border-bottom-color: var(--color-white);
  font-weight: bold;
  background-color: transparent;
}

.bg-abbl-gradient {
  background-image: linear-gradient(to right, #3269CD, #37BECD);
}

.bg-abbl-gradient-revert {
  background-image: linear-gradient(to left, #3269CD, #37BECD);
}

.bg-abbl-gradient-grain {
  position: relative;
}

.bg-abbl-gradient-grain > * {
  position: relative;
  z-index: 1;
}

.bg-abbl-gradient-grain:before,
.bg-abbl-gradient-grain:after {
  content: '';
  z-index: 0;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.bg-abbl-gradient-grain:before {
  background-image: url('../images/decorations/backgrounds/mesh-gradient.svg');
  background-size: cover;
  background-position: center;
}

.bg-abbl-gradient-grain:after {
  /* background-image: url('../images/decorations/backgrounds/noise.png'); */
  /* background-image: url('../images/decorations/backgrounds/noise-1.png'); */
  background-image: url('../images/decorations/backgrounds/noise-gaussian.png');
  background-size: 250px;
  opacity: 20%;
}


.glow-neon-green {
  filter: drop-shadow(0 0 3.125rem rgba(122, 255, 208, 1));
}

.swiper-container {
  position: relative;
}

.swiper-container.with-fade .swiper {
  overflow: visible;
}

.swiper-container .swiper-pagination {
  position: static;
  padding-top: 1.25rem;
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  @variant lg {
    gap: 0.5rem;
  }
}

.swiper-container .swiper-slide {
  height: auto;
}

.swiper-container .swiper-pagination-bullet {
  opacity: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.1875rem;
  height: 1.1875rem;
  margin: 0 !important;
  font-size: 0.625rem;
  background-color: transparent;
  counter-increment: pagination;
  @variant lg {
    border: 2px solid var(--color-white);
    width: 0.625rem;
    height: 0.625rem;
  }
}

.swiper-container .swiper-pagination-bullet:before {
  display: block;
  content: counter(pagination);
  font-weight: bold;
  color: var(--color-white);
  transition: .2s linear;
  @variant lg {
    display: none;
  }
}

.swiper-container .swiper-pagination-bullet-active {
  border-color: var(--color-neon-green);
  background-color: var(--color-neon-green);
  transition: .2s linear;
  @variant lg {
    border-color: var(--color-yellow);
    background-color: var(--color-yellow);
  }
}

.swiper-container .swiper-pagination-bullet-active:before {
  color: var(--color-navy-blue);
}

.swiper-container .swiper-button-prev,
.swiper-container .swiper-button-next {
  font-size: 2rem;
}

.swiper-container .swiper-button-prev:after,
.swiper-container .swiper-button-next:after {
  content: none;
}

.swiper-container .swiper-button-prev,
.swiper-container .swiper-button-next {
  top: calc(50% - 15px);
  width: auto;
  height: auto;
  outline-width: 0;
  outline-offset: 3px;
  outline-color: var(--color-yellow);
}

.swiper-container .swiper-button-prev:focus-within,
.swiper-container .swiper-button-next:focus-within {
  border: none;
  outline-width: 3px;
}

.swiper-container .swiper-button-prev {
  left: 0;
  @variant lg {
    left: -4.25rem;
  }
}

.swiper-container .swiper-button-next {
  right: 0;
  @variant lg {
    right: -4.25rem;
  }
}

.swiper-container.with-fade .swiper-slide {
  transition: opacity .3s ease;
  user-select: none;
}

.swiper-container.with-fade .swiper-slide:not(.active) {
  opacity: 0;
  pointer-events: none;
}

#home-hot-topics-slider .swiper-wrapper {
  align-items: center;
}

#home-hot-topics-slider .swiper-button-prev {
  @variant max-lg {
    left: 1rem;
  }
}

#home-hot-topics-slider .swiper-button-next {
  @variant max-lg {
    right: 1rem;
  }
}

#slider-hot-topics-list li.active p {
  font-weight: theme('fontWeight.bold');
}

#slider-hot-topics-list li.active i {
  width: theme('spacing.10');
}

.archive-filters-contents {
  transition: max-height .3s ease;
}

.archive-filters.archive-filters--open .archive-filters-contents {
  max-height: 400px !important;
}

.home-topics-slider-image {
  opacity: 0;
  transition: opacity .2s .1s linear;
}

.home-topics-slider-image.active {
  opacity: 1;
  transition: opacity .2s linear;
}

body .ps__rail-y,
body .ps__thumb-y {
  width: 0.625em;
}

body .ps__rail-y,
body .ps__rail-x {
  opacity: 1;
  background-color: rgba(255, 255, 255, .5);
  border-radius: 0.625rem;
}

body .ps--active-x > .ps__rail-x,
body .ps--active-y > .ps__rail-y {
  background-color: rgba(255, 255, 255, .5);
}

body .ps__thumb-y,
body .ps__thumb-x {
  opacity: 1;
  @apply bg-yellow;
}

body .ps__thumb-y {
  right: 0;
}

body .ps__rail-x,
body .ps__thumb-x {
  height: 0.625em;
}

body .ps:hover > .ps__rail-x,
body .ps:hover > .ps__rail-y,
body .ps--focus > .ps__rail-x,
body .ps--focus > .ps__rail-y,
body .ps--scrolling-x > .ps__rail-x,
body .ps--scrolling-y > .ps__rail-y {
  opacity: 1;
  width: 0.625em;
}

body .ps__rail-y:hover > .ps__thumb-y,
body .ps__rail-y:focus > .ps__thumb-y,
body .ps__rail-y.ps--clicking .ps__thumb-y {
  width: 0.625rem;
  background-color: var(--color-yellow);
}

.single-event-grid {

}
