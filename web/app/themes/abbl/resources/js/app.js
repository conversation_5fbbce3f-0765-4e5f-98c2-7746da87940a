import.meta.glob([
  '../images/**',
  '../fonts/**',
]);

import Swiper from 'swiper';
import {A11y, Pagination, Navigation, Autoplay} from 'swiper/modules';
import PerfectScrollbar from 'perfect-scrollbar';
import 'perfect-scrollbar/css/perfect-scrollbar.css';
import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import Sticky from 'sticky-js';

 /**
 * Create a range of numbers.
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#sequence_generator_range
 * @param {number} start The first number in the range.
 * @param {number} stop The last number in the range.
 * @param {number} step The step between each number in the range.
 * @returns {number[]} A range of numbers.
 */
const range = (start, stop, step = 1) => Array.from(
    { length: (stop - start) / step + 1 },
    (_, i) => start + i * step
);

const fadeHooks = {
    afterInit: function () {
        range(this.activeIndex, this.activeIndex + (this.slidesPerViewDynamic() - 1)).forEach(i => {
            if (this.slides[i]) {
                this.slides[i].classList.add('active');
            }
        });
    },
    slideChange: function () {
        this.slides.forEach(s => s.classList.remove('active'));

        range(this.activeIndex, (this.activeIndex + this.slidesPerViewDynamic() - 1)).forEach(i => {
            if (this.slides[i]) {
                this.slides[i].classList.add('active');
            }
        });
    }
};

function createBasicSlider(selector) {
    if (!document.querySelector(`${selector} .swiper`)) {
        return;
    }

    return new Swiper(`${selector} .swiper`, {
        modules: [A11y, Pagination, Navigation],
        slidesPerView: 1,
        spaceBetween: 36,
        loop: true,
        keyboard: {
            enabled: true,
            onlyInViewport: true,
        },
        pagination: {
            el: `${selector} .swiper-pagination`,
            clickable: true,
        },
        navigation: {
            nextEl: `${selector} .swiper-button-next`,
            prevEl: `${selector} .swiper-button-prev`,
        },
        on: fadeHooks,
        breakpoints: {
            800: {
                slidesPerView: 2,
                spaceBetween: 20,
            },
            1200: {
                slidesPerView: 3,
                spaceBetween: 20,
            },
        },
    });
}

function homeHotTopicsSlider() {
    const id = '#home-hot-topics-slider';

    const listItems = Array.from(document.querySelectorAll('#slider-hot-topics-list li'));
    const images = Array.from(document.querySelectorAll('.home-topics-slider-image'));

    const slider = new Swiper(`${id} .swiper`, {
        modules: [A11y, Pagination, Navigation, Autoplay],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
            delay: 5000,
            pauseOnMouseEnter: true,
        },
        pagination: {
            el: `${id} .swiper-pagination`,
            clickable: true,
        },
        navigation: {
            nextEl: `${id} .swiper-button-next`,
            prevEl: `${id} .swiper-button-prev`,
        },
        on: {
            afterInit: function () {
                this.slides[this.activeIndex].classList.add('active');

                listItems.forEach(item => {
                    const index = Number(item.dataset.index);

                    item.addEventListener('click', () => {
                        listItems.forEach(i => i.classList.remove('active'));

                        item.classList.add('active');

                        slider.slideTo(index);
                    });
                });
            },
            slideChange: function () {
                this.slides.forEach(s => s.classList.remove('active'));

                this.slides[this.activeIndex].classList.add('active');

                listItems.forEach(item => item.classList.remove('active'));
                images.forEach(i => i.classList.remove('active'));

                listItems[this.realIndex].classList.add('active');
                images[this.realIndex].classList.add('active');
            },
        },
    });
}

function archiveFilters() {
    const elements = Array.from(document.querySelectorAll('.archive-filters'));

    elements.forEach(element => {
        const toggle = element.querySelector('.archive-filters-toggle');

        toggle.addEventListener('click', () => {
            element.classList.toggle('archive-filters--open');
        });
    });
}

function siteSectionSwitcher() {
    const elements = [
        { button: document.querySelector('#header-professionals-button'), storageValue: 'professional' },
        { button: document.querySelector('#header-consumers-button'), storageValue: 'consumer' },
    ]

    elements.forEach(({ button, storageValue }) => {
        button?.addEventListener('click', (e) => {
            e.preventDefault();

            window.localStorage.setItem('abblUserType', storageValue);

            window.location = e.target.href;
        });
    });
}

function mainMenu() {
    const body = document.querySelector('body');
    const primaryMenuButton = document.querySelector('#primary-menu-button');
    const subMenuInfos = document.querySelector('#sub-menu-infos');
    const subMenuClose = document.querySelector('#sub-menu-infos-close');
    const subMenuLabels = document.querySelector('#sub-menu-infos-labels');

    primaryMenuButton.addEventListener('click', () => {
        body.classList.toggle('is-primary-menu-active');
    });

    const firstLevelMenus = Array.from(document.querySelectorAll('#primary-menu > li.menu-item-has-children'));
    const allCloseButtons = Array.from(document.querySelectorAll('#primary-menu li.menu-item-close-button button'));

    firstLevelMenus.forEach((clickedItem) => {
        const link = clickedItem.querySelector('a');

        link.addEventListener('click', (e) => {
            e.preventDefault();

            firstLevelMenus.filter(m => m !== clickedItem).forEach(m => m.classList.remove('active'));
            clickedItem.classList.toggle('active');

            if (firstLevelMenus.filter(m => m.classList.contains('active')).length <= 0) {
                body.classList.remove('primary-sub-menu-is-open');
            } else {
                body.classList.add('primary-sub-menu-is-open');
            }

            updateInfos();

            return false;
        });
    });

    allCloseButtons.forEach((button) => {
        button.addEventListener('click', (e) => {
            firstLevelMenus.forEach(m => m.classList.remove('active'));
            body.classList.remove('primary-sub-menu-is-open')

            updateInfos();
        });
    });

    subMenuClose.addEventListener('click', () => {
        firstLevelMenus.forEach(m => m.classList.remove('active'));
        body.classList.remove('primary-sub-menu-is-open')

        updateInfos();
    });

    function updateInfos() {
        const activeSubMenus = document.querySelectorAll('#primary-menu > li.menu-item-has-children > ul.is-open');

        subMenuInfos.classList.toggle('hidden', activeSubMenus.length === 0);

        const ancestor = document.querySelector('#primary-menu > li.current-menu-ancestor');

        if (ancestor) {
            subMenuLabels.innerHTML = `<span>Home /</span> <span>${ancestor.querySelector('a').innerText}</span>`;
        }
    }
}

function sliderTabJumpOut() {
    window.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            const slider = document.activeElement?.closest('.swiper-slide')?.closest('.swiper')?.swiper;

            if (slider && (slider.realIndex === slider.slides.length - 1)) {
                console.log('Tab out now !');
                // TODO: Implement tab out for sliders
            }
        }
    });
}

function linkedinFeed() {
    const scrollContainer = document.querySelector('#linkedin-feed');
    let ps = null;

    const createOrUpdateScrollBar = () => {
        ps?.destroy();

        if (window.innerWidth < 1024) {
            ps = new PerfectScrollbar(scrollContainer, {
                suppressScrollX: false,
                suppressScrollY: true,
            });
        } else {
            ps = new PerfectScrollbar(scrollContainer, {
                suppressScrollX: true,
                suppressScrollY: false,
            });
        }
    }

    if (scrollContainer) {
         createOrUpdateScrollBar();

        window.addEventListener('resize', () => createOrUpdateScrollBar());
    }
}

function singleEventImageAdjustment() {
    const registerCard = document.querySelector('#register-card');
    const eventCover = document.querySelector('#event-cover');

    if (registerCard && eventCover) {
        eventCover.style.height = `${registerCard.offsetHeight}px`;
    }
}

function singleEventStickyRegister() {
    const registerCard = document.querySelector('#register-card');
    const fakeRegisterCard = document.querySelector('#fake-register-card');

    if (fakeRegisterCard) {
        fakeRegisterCard.style.height = `${registerCard.offsetHeight}px`;
    }

    if (registerCard) {
        new Sticky('#register-card', {
            marginTop: 180,
            stickyFor: 1200,
            stickyClass: 'is-sticky',
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.page-template-template-homepage')) {
        return;
    }

    createBasicSlider('#home-news-slider');
    createBasicSlider('#home-publications-slider');
    createBasicSlider('[data-events-slider]');

    homeHotTopicsSlider();

    archiveFilters();

    siteSectionSwitcher();

    mainMenu();

    sliderTabJumpOut();

    linkedinFeed();

    singleEventImageAdjustment();

    singleEventStickyRegister();

    tippy('[data-tippy-content]');
});
