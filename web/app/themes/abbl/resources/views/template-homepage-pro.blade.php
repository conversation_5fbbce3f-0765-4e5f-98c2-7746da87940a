@php use App\Modules\PublicationsModule; @endphp
{{--
    Template Name: Professionals homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-featured-hot-topics-slider/>

        @while(have_rows('intro'))
            @php the_row(); @endphp

            <x-section class="py-16 my-0 md:my-0 bg-light-blue">
                <x-container class="relative grid lg:grid-cols-[2fr_1fr] lg:items-center lg:gap-24">
                    <div class="space-y-4 mb-14 lg:mb-0">
                        @if($tagline = get_sub_field('tagline'))
                            <h2 class="mb-2.5 text-[0.9375rem] font-bold uppercase">{{ $tagline }}</h2>
                        @endif
                        @if($subtitle = get_sub_field('subtitle'))
                            <p class="text-[1.25rem] font-headings font-bold">{{ $subtitle }}</p>
                        @endif
                        @if($content = get_sub_field('content'))
                            <div class="wysiwyg">
                                {!! $content !!}
                            </div>
                        @endif

                        @if(have_rows('key_figures'))
                            @php
                                $key_figure_count = count(get_field('intro_key_figures'));
                            @endphp

                            <ul class="flex justify-between md:justify-start md:items-center md:gap-14 my-12">
                                @while(have_rows('key_figures'))
                                    @php the_row(); @endphp

                                    <li>
                                        @if($number = get_sub_field('number'))
                                            <p class="text-[1.5rem] leading-none font-bold">{{ $number }}</p>
                                        @endif
                                        @if($title = get_sub_field('title'))
                                            <p class="text-[0.875rem] uppercase">{{ $title }}</p>
                                        @endif
                                    </li>
                                    @if(!(get_row_index() >= $key_figure_count))
                                        <li aria-hidden="true" class="w-0.5 h-8 bg-navy-blue"></li>
                                    @endif
                                @endwhile
                            </ul>
                        @endif

                        <div class="grid md:grid-cols-2 md:flex gap-8">
                            @if($button = get_sub_field('become_member_button'))
                                <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                    {{ $button['title'] }}
                                </x-link-button>
                            @endif
                            @if($button = get_sub_field('learn_more_button'))
                                <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}" class="bg-white font-semibold">
                                    {{ $button['title'] }}
                                </x-link-button>
                            @endif
                        </div>
                    </div>
                    <div class="relative p-8 lg:py-18 lg:px-14 rounded-lg bg-navy-blue">
                        <div class="opacity-25 hidden lg:block absolute top-0 left-[6rem] w-full h-full rounded-lg bg-size-[100%] bg-center" style="background-image: url('{{ Vite::asset('resources/images/decorations/diagonals-navy-blue.svg') }}')"></div>

                        @if($quick_access_title = get_sub_field('quick_access_title'))
                            <h3 class="z-10 relative mb-8 text-[1.75rem] font-bold text-neon-green">{{ $quick_access_title }}</h3>
                        @endif

                        @if(have_rows('quick_access_links'))
                            <ul class="z-10 relative grid md:grid-cols-2 lg:grid-cols-1 gap-5">
                                @while(have_rows('quick_access_links'))
                                    @php the_row(); @endphp

                                    @if($button = get_sub_field('button'))
                                        <li>
                                            <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}" class="flex items-center justify-start gap-4 pl-5 pr-2 py-4 rounded font-medium bg-white outline-yellow">
                                                @if($icon = get_sub_field('icon'))
                                                    <i class="{{ $icon }}"></i>
                                                @endif
                                                <span>{{ $button['title'] }}</span>
                                            </x-link-button>
                                        </li>
                                    @endif
                                @endwhile
                            </ul>
                        @endif
                    </div>
                </x-container>
            </x-section>
        @endwhile

        <div class="py-10 md:py-20 bg-abbl-gradient-grain">
            @while(have_rows('latest_news'))
                @php the_row(); @endphp

                <x-section class="md:mt-0">
                    <x-container>
                        @if($latest_news_title = get_sub_field('title'))
                            <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">
                                {{ $latest_news_title }}
                            </h2>
                        @endif

                        <x-news-slider :postsPerPage="get_sub_field('max_news') ?: 6" />

                        @if($latest_news_button = get_sub_field('bottom_button'))
                            <div class="flex justify-center lg:justify-start mt-5">
                                <x-link-button class="w-full md:w-auto" href="{{ $latest_news_button['url'] }}" target="{{ $latest_news_button['target'] ?? '_self' }}">
                                    {!! $latest_news_button['title'] !!}
                                </x-link-button>
                            </div>
                        @endif
                    </x-container>
                </x-section>
            @endwhile

            <x-section class="relative">
                {{-- START YELLOW DIAGONALS --}}
                <img
                    class="absolute right-0 top-[4.5rem] hidden lg:block w-[145px] h-[699px] select-none"
                    src="{{ Vite::asset('resources/images/decorations/home-yellow-diagonals.png') }}"
                    alt=""
                >
                {{-- END YELLOW DIAGONALS --}}

                <x-container class="relative lg:grid lg:grid-cols-[1fr_28.5625rem] gap-12 lg:gap-16">
                    <img
                        class="absolute -bottom-[3.75rem] -left-[11.25rem] w-[31px] h-[340px] hidden lg:block point-events-none bg-fit bg-center select-none"
                        src="{{ Vite::asset('resources/images/decorations/home-arrow-white.png') }}"
                        alt=""
                    >

                    {{-- START EVENTS --}}
                    @while(have_rows('events'))
                        @php the_row(); @endphp

                        <div class="mb-12 lg:mb-0">
                            @if($events_title = get_sub_field('title'))
                                <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold">{{ $events_title }}</h2>
                            @endif

                            @php
                            $events_query = new WP_Query([
                                'post_type' => CPT_EVENT,
                                'posts_per_page' => get_sub_field('max_events') ?: 3,
                            ]);

                            $i = 0;
                            @endphp

                            @if($events_query->have_posts())
                                <div id="events-grid" class="hidden lg:grid lg:grid-cols-2 gap-4">
                                    @while($events_query->have_posts())
                                        @php
                                            $events_query->the_post();
                                            $is_featured = get_field('is_featured');
                                        @endphp

                                        <div @class(['relative', 'lg:col-span-2' => $is_featured])>
                                            @if($i === 0)
                                                <div class="hidden lg:block absolute top-0 left-[-9rem] h-full w-[5.25rem] bg-size-[300%] bg-center" style="background-image: url('{{ Vite::asset('resources/images/decorations/diagonals-white.png') }}');"></div>
                                            @endif

                                            <x-event-card
                                                @class([
                                                    'h-full',
                                                    'relative z-10' => !$is_featured,
                                                ])
                                            />
                                        </div>

                                        @php $i++; @endphp
                                    @endwhile

                                    @php wp_reset_postdata(); @endphp
                                </div>
                            @else
                                <p>{{ __('No upcoming events.') }}</p>
                            @endif

                            @php $events_query->rewind_posts(); @endphp

                            @if($events_query->have_posts())
                                <div class="lg:hidden">
                                    <div id="events-slider" data-events-slider class="swiper-container with-fade">
                                        <!-- Slider main container -->
                                        <div class="swiper">
                                            <!-- Additional required wrapper -->
                                            <div class="swiper-wrapper">
                                                @while($events_query->have_posts())
                                                    @php
                                                        $events_query->the_post();
                                                    @endphp

                                                    <div class="swiper-slide">
                                                        <x-event-card class="h-full" />
                                                    </div>
                                                @endwhile
                                            </div>

                                            <div class="swiper-pagination"></div>

                                            <!-- If we need navigation buttons -->
                                            <div class="swiper-button-prev">
                                                <i class="fas fa-square-chevron-left text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
                                            </div>
                                            <div class="swiper-button-next">
                                                <i class="fas fa-square-chevron-right text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @php wp_reset_postdata(); @endphp

                            <div class="mt-8 lg:mt-14">
                                @if($button = get_sub_field('bottom_button'))
                                    <x-link-button class="w-full md:w-auto" href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                        {{ $button['title'] }}
                                    </x-link-button>
                                @endif
                            </div>
                        </div>
                    @endwhile
                    {{-- END EVENTS --}}

                    {{-- START LINKEDIN FEED --}}
                    @while(have_rows('linkedin_feed'))
                        @php the_row(); @endphp

                        <div class="relative">
                            @if($linkedin_title = get_sub_field('title'))
                                <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">{{ $linkedin_title }}</h2>
                            @endif
                            <div id="linkedin-feed" class="relative overflow-auto lg:pr-[24px] lg:w-[calc(100%_+_2rem)]">
                                <div class="absolute top-0 bottom-0 left-[47.75rem] hidden lg:block w-[544px] h-full rounded-lg bg-size-[100%] bg-center" style="background-image: url('{{ Vite::asset('resources/images/decorations/diagonals-yellow.png') }}')"></div>

                                <div id="linkedin-feed-posts" class="overflow-hidden grid max-w-full rounded-lg bg-light-blue">
                                    <div class="relative flex lg:block gap-4 p-4">
                                        @foreach(range(0, 3) as $i)
                                            <div class="shrink-0 grow-0 w-full md:w-1/2 lg:w-full px-5 py-4 lg:mb-4 lg:last:mb-0 rounded-lg text-black bg-white">
                                                <div class="flex items-center gap-4 mb-4">
                                                    <div class="aspect-square flex justify-center items-center w-14 p-2 rounded-full bg-abbl-gradient">
                                                        <img src="{{ Vite::asset('resources/images/abbl-logo.svg') }}" alt="">
                                                    </div>
                                                    <div>
                                                        <h3 class="font-bold">ABBL</h3>
                                                        <p class="text-[0.875rem]">Published on 10.20.2025</p>
                                                    </div>
                                                    <div class="ml-auto self-start">
                                                        <i class="fab fa-linkedin text-[1.5rem] text-[#1E66BF]"></i>
                                                    </div>
                                                </div>
                                                <div class="mb-6 space-y-[0.875rem] text-[0.875rem] line-clamp-2 lg:line-clamp-none">
                                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet. Nam eu nibh id tortor placerat venenatis in ac quam. Integer nec est enim.</p>
                                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet.</p>
                                                </div>
                                                <div>
                                                    <img src="{{ Vite::asset('resources/images/needs-integration/linkedin-feed-image.jpg') }}" alt="" class="rounded">
                                                </div>
                                                <div class="h-px my-5 bg-[#EAEAEA]"></div>
                                                <div class="flex text-[0.875rem] [&>*]:flex [&>*]:items-center [&>*]:gap-2">
                                                    <a href="#" class="mr-7 text-[#8E8E8E]">
                                                        <i class="fas fa-heart text-[1rem]"></i>
                                                        <span>468</span>
                                                    </a>
                                                    <a href="#" class="text-[#8E8E8E]">
                                                        <i class="fab fa-rocketchat text-[1rem]"></i>
                                                        <span>12</span>
                                                    </a>
                                                    <a href="#" class="ml-auto">
                                                        <span>Share</span>
                                                        <i class="fas fa-share text-[1rem]"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            @if($button = get_sub_field('bottom_button'))
                                <div class="flex justify-center lg:justify-start mt-14">
                                    <x-link-button class="w-full md:w-auto" href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                        {{ $button['title'] }}
                                    </x-link-button>
                                </div>
                            @endif
                        </div>
                    @endwhile
                    {{-- END LINKEDIN FEED --}}

                    {{-- PERCENTAGE IMAGE --}}
                    <img
                        class="absolute left-[-19.4375rem] top-[2.25rem] w-[12.375rem] h-[16.3125rem] hidden lg:block point-events-none bg-fit bg-center select-none"
                        src="{{ Vite::asset('resources/images/decorations/percentage.svg') }}"
                        alt=""
                    >
                    {{-- END PERCENTAGE IMAGE --}}

                    {{-- ARROW IMAGE --}}
                    <img
                        class="absolute right-[5rem] -bottom-[7.8625rem] w-[171px] h-[121px] hidden lg:block point-events-none bg-contain bg-center select-none"
                        src="{{ Vite::asset('resources/images/decorations/yellow-arrow.png') }}"
                        alt=""
                    >
                    {{-- END ARROW IMAGE --}}
                </x-container>


            </x-section>

            @while(have_rows('publications'))
                @php the_row(); @endphp

                {{-- START PUBLICATIONS --}}
                <x-section class="md:mb-0">
                    <x-container class="relative">
                        {{-- START YELLOW BUBBLE --}}
                        <img
                            class="absolute left-[-23.1875rem] bottom-[1rem] hidden lg:block w-[318px] h-[324px] select-none"
                            src="{{ Vite::asset('resources/images/decorations/yellow-bubble.png') }}"
                            alt=""
                        >
                        {{-- END YELLOW BUBBLE --}}

                        {{-- START GREEN GRAPH BARS --}}
                        <img
                            class="absolute right-[-23.75rem] bottom-[-13.375rem] hidden lg:block w-[352px] h-[316px] select-none"
                            src="{{ Vite::asset('resources/images/decorations/graph-bars.png') }}"
                            alt=""
                        >
                        {{-- END GREEN GRAPH BARS --}}

                        @if($title = get_sub_field('title'))
                            <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold text-balance">
                                {{ $title }}
                            </h2>
                        @endif

                        <x-publications-slider :per-page="get_sub_field('max_publications')" />

                        @if($publications_button = get_sub_field('bottom_button'))
                            <div class="relative z-10 flex justify-center lg:justify-start mt-5">
                                <x-link-button class="w-full md:w-auto" href="{{ $publications_button['url'] }}" target="{{ $publications_button['target'] ?? '_self' }}">
                                    {{ $publications_button['title'] }}
                                </x-link-button>
                            </div>
                        @endif
                    </x-container>
                </x-section>
                {{-- END PUBLICATIONS --}}
            @endwhile
        </div>
    @endwhile
@endsection

@push('footer-scripts')
    <script>
        const eventsGrid = document.querySelector('#events-grid');
        const linkedinFeed = document.querySelector('#linkedin-feed');
        const feedImages = Array.from(linkedinFeed.querySelectorAll('img'));

        function resizeLinkedInFeedHeight() {
            if (window.innerWidth < 1024) {
                linkedinFeed.style.height = 'auto';
                return;
            }

            const eventsGridHeight = eventsGrid.getBoundingClientRect().height;
            linkedinFeed.style.height = `${eventsGridHeight}px`;
        }

        resizeLinkedInFeedHeight();

        window.addEventListener('resize', () => resizeLinkedInFeedHeight());

        feedImages.forEach(img => {
            img.addEventListener('load', () => resizeLinkedInFeedHeight());
        });

        document.addEventListener('load', () => resizeLinkedInFeedHeight());
        document.addEventListener('DOMContentLoaded', () => resizeLinkedInFeedHeight());
    </script>
@endpush
