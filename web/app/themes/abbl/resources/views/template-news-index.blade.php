@php use App\Modules\PostsModule; @endphp
{{--
  Template Name: News index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
            the_post();
        @endphp

        <x-flexibles.hero title="ABBL News">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar
                aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <div class="py-14 lg:py-30 bg-abbl-gradient-grain">
            {{--
            <x-section class="py-0 mt-0 lg:mt-0 lg:py-0">
                <x-container>
                    <x-breadcrumbs :segments="['ABBL Pro', 'News']"/>
                </x-container>
            </x-section>
            --}}

            <x-section class="my-0 lg:my-0">
                <x-container class="mb-8">
                    <form method="GET">
                        <x-archive-filters>
                            @php
                                $topics = get_terms([
                                    'post_type' => 'post',
                                    'taxonomy' => 'category',
                                 ]);

                                $types_of_content = get_terms([
                                    'post_type' => 'post',
                                    'taxonomy' => TAX_POST_CONTENT_TYPE,
                                ]);

                                $authors = get_users();

                                $publication_dates = abbl_get_publication_dates();
                            @endphp

                            <x-select onchange="this.closest('form').submit();">
                                <option value="">Topic</option>
                                @foreach ($topics as $topic)
                                    <option value="{{ $topic->term_id }}">{{ $topic->name }}</option>
                                @endforeach
                            </x-select>

                            <x-select onchange="this.closest('form').submit();">
                                <option value="">Type of content</option>
                                @foreach ($types_of_content as $content)
                                    <option value="{{ $content->term_id }}">{{ $content->name }}</option>
                                @endforeach
                            </x-select>

                            <x-select onchange="this.closest('form').submit();">
                                <option value="">Publication date</option>
                                @foreach ($publication_dates as $date)
                                    <option
                                        value="{{ $date->year }}-{{ $date->month }}">{{ $date->monthName }} {{ $date->year }}</option>
                                @endforeach
                            </x-select>

                            <x-select onchange="this.closest('form').submit();">
                                <option value="">Author</option>
                                @foreach ($authors as $author)
                                    <option value="{{ $author->ID }}">{{ $author->display_name }}</option>
                                @endforeach
                            </x-select>

                            <x-button type="submit" class="w-full lg:w-auto ml-auto">{{ __('Clear all filters', 'abbl') }}</x-button>
                        </x-archive-filters>
                    </form>
                </x-container>

                @php
                    $posts_per_page = 12;
                    $news_query = PostsModule::query([
                        'post_type' => 'post',
                        'posts_per_page' => $posts_per_page,
                    ]);

                    $i = 0;
                @endphp

                <x-container class="mb-8">
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @while($news_query->have_posts())
                            @php $news_query->the_post(); @endphp

                            {{-- Insert the newsletter aside when we're halfway through --}}
                            @if($i === $posts_per_page / 2)
                    </div>

                    <div class="my-8">
                        <x-flexibles.newsletter/>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        @endif

                        <x-news-card/>

                        @php $i++; @endphp
                        @endwhile
                    </div>

                    <div class="grid grid-flow-row justify-stretch lg:grid-flow-col lg:justify-center gap-5 mt-6 lg:mt-14">
                        <x-link-button href="#" class="w-full lg:w-auto text-white bg-navy-blue/75 hover:text-navy-blue">
                            <i class="fas fa-angles-down"></i>
                            <span>{{ __('Load more news') }}</span>
                        </x-link-button>
                    </div>
                </x-container>
            </x-section>
        </div>
    @endwhile
@endsection
