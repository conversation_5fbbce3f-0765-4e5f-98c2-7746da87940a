{{--
    Template Name: Publications index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', 'Publications']" />
            </x-container>
        </x-section>

        <x-section>
            <x-container>
                <div class="flex justify-between p-4 bg-abbl-gradient-grain">
                    <div class="grid grid-cols-[1fr_1fr_2fr] gap-8">
                        <select class="block px-4 py-2 bg-gray-100" name="" id="">
                            <option value="">Publication type</option>
                        </select>
                        <select class="block px-4 py-2 bg-gray-100" name="" id="">
                            <option value="">Topic</option>
                        </select>
                        <input class="block px-4 py-2 bg-gray-100" type="text" placeholder="Search by name">
                    </div>
                    <a class="px-4 py-2 bg-gray-400" href="#">Filter publications</a>
                </div>
            </x-container>
        </x-section>

        @php
        $sections = [
            [
                'title' => 'Guidelines',
                'button' => 'View all guidelines'
            ],
            [
                'title' => 'Annual reports',
                'button' => 'View all annual reports'
            ],
            [
                'title' => 'Position papers',
                'button' => 'View all position papers'
            ],
            [
                'title' => 'Uncategorized publications',
                'button' => 'View all uncategorized publications'
            ],
        ];
        @endphp

        @foreach ($sections as $section)
            <x-section>
                <x-container>
                    <h2 class="mb-8 text-2xl font-bold">{{ $section['title'] }}</h2>
                    <div class="grid grid-cols-3 gap-4 mb-8">
                        @foreach (range(0, 5) as $i)
                            <x-publication-card />
                        @endforeach
                    </div>
                    <div>
                        <a class="inline-block px-4 py-2 bg-gray-400" href="#">{{ $section['button'] }}</a>
                    </div>
                </x-container>
            </x-section>
        @endforeach
    @endwhile
@endsection
