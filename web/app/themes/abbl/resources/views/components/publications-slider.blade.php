@props([
    'perPage' => 6,
    'reducedContent' => false,
])

@php
    use App\Modules\PublicationsModule;

    $publications_query = PublicationsModule::query([
        'posts_per_page' => $perPage,
    ]);
@endphp

<div id="home-publications-slider" class="swiper-container with-fade">
    <!-- Slider main container -->
    <div class="swiper">
        <!-- Additional required wrapper -->
        <div class="swiper-wrapper">
            <!-- Slides -->
            @while($publications_query->have_posts())
                @php $publications_query->the_post(); @endphp

                <div class="swiper-slide">
                    <x-publication-card class="h-full" :reducedContent="$reducedContent" :isPdf="$publications_query->current_post === 1"/>
                </div>
            @endwhile
        </div>
    </div>

    <div class="swiper-pagination"></div>

    <!-- If we need navigation buttons -->
    <div class="swiper-button-prev">
        <i class="fas fa-square-chevron-left text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
    <div class="swiper-button-next">
        <i class="fas fa-square-chevron-right text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
</div>
