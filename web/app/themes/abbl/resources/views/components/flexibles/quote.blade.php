<blockquote class="lg:grid grid-cols-[19rem_1fr] gap-6.5 w-full mx-auto rounded-lg bg-neon-green">
    <div class="relative h-[10.625rem] lg:h-auto lg:w-auto">
        @if($image = get_sub_field('quote_author_image'))
            {{-- DESKTOP IMAGES --}}
            <img
                class="hidden lg:block absolute bottom-0 left-0 right-0 w-[13.25rem] mx-auto"
                src="{{ $image['url'] }}"
                alt="{{ $image['alt'] }}"
            >
            <img
                class="hidden lg:block absolute bottom-0 left-0 right-0 w-[19rem] mx-auto"
                src="{{ Vite::asset('resources/images/decorations/quote-author.svg') }}"
                alt=""
            >
            {{-- END DESKTOP IMAGES --}}
            {{-- MOBILE IMAGES --}}
            <img
                class="lg:hidden absolute bottom-0 left-0 right-0 w-[15.375rem] mx-auto"
                src="{{ Vite::asset('resources/images/needs-integration/example-quote-author-mobile.png') }}"
                alt="{{ $image['alt'] }}"
            >
            <img
                class="lg:hidden absolute bottom-0 left-0 right-0 w-[22.1875rem] pr-[2.25rem] mx-auto"
                src="{{ Vite::asset('resources/images/decorations/quote-author-mobile.svg') }}"
                alt=""
            >
            {{-- END MOBILE IMAGES --}}
        @endif
    </div>
    <div class="px-8 lg:px-0 py-6 text-left md:text-center lg:text-left">
        <div class="mb-6 font-headings text-[1.5rem] lg:text-[1.875rem] leading-[1.25] font-light text-balance">
            {!! get_sub_field('quote_content') !!}
        </div>
        <cite class="space-y-3 not-italic">
            <p class="block mb-0 leading-[1] text-[1.5rem] font-bold">{{ get_sub_field('quote_author_name') }}</p>
            @if($job = get_sub_field('quote_author_job'))
                <p class="block">{{ $job }}</p>
            @endif
        </cite>
    </div>
</blockquote>
