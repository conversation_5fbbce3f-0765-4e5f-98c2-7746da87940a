@props([
    'title' => null,
    'content' => null,
    'showLinkedin' => false,
])

@php
$privacy_policy_url = get_permalink(get_field('page_privacy_policy', 'options'));
@endphp

<aside class="lg:grid lg:grid-cols-[4fr_9fr] lg:gap-[4.5rem] px-8 pb-10 pt-14 lg:px-12 lg:pt-16 lg:pb-8 rounded-lg bg-abbl-gradient">
    <h2 class="mb-11 text-[1.75rem] leading-[1] font-headings font-bold text-balance text-neon-green">
        @if($title)
            {!! $title !!}
        @else
            {!! get_field('newsletter_default_title', 'options') !!}
        @endif
    </h2>
    <form action="#">
        <div class="flex gap-5">
            <input type="text" placeholder="Your email address" class="w-full md:min-w-[17.8125rem] h-12 px-4 rounded text-[0.875rem] text-navy-blue bg-white placeholder:italic">
            <x-button type="submit" class="hidden lg:block">{{ __('Subscribe', 'abbl') }}</x-button>
        </div>
        <label class="flex items-center gap-7 mt-7 text-white">
            <input type="checkbox" name="optin" id="" class="w-[1.125rem] h-[1.125rem]">
            <span class="text-[0.625rem]">{!! sprintf(__('I have read and I accept the <a href="%s" class="!underline">ABBL Privacy Policy</a>', 'abbl'), 'https://google.com') !!}</span>
        </label>
        <x-button type="submit" class="lg:hidden w-full mt-7">{{ __('Subscribe', 'abbl') }}</x-button>
    </form>
    @if($showLinkedin)
        <div class="flex gap-4">
            <a href="#" class="flex items-center gap-4 font-bold">
                <i class="fab fa-linkedin text-lg"></i>
                {{ __('Follow us on LinkedIn for real-time updates', 'abbl') }}
            </a>
        </div>
    @endif
</aside>
