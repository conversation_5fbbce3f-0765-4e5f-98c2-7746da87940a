<div class="space-y-7 text-[1.125rem]">
    @if($title = get_sub_field('related_news_title'))
        <div class="wysiwyg">
            <h2>{{ $title }}</h2>
        </div>
    @endif
    <ul>
        @php
            $news = get_sub_field('related_news_news');
        @endphp

        @if($news && count($news))
            @foreach($news as $news_item)
                @php
                    $news_category = get_the_terms($news_item, 'category')[0] ?? null;
                @endphp

                <li class="lg:grid lg:grid-cols-[5fr_7fr] gap-5 rounded-lg bg-light-blue">
                    <div class="relative w-full lg:w-auto aspect-[387/208]">
                        @if($thumbnail_url = get_the_post_thumbnail_url($news_item))
                            <img src="{{ $thumbnail_url }}" alt="{{ get_the_post_thumbnail_caption($news_item) }}" class="absolute inset-0 w-full h-full object-cover object-center rounded-lg">
                        @endif
                    </div>
                    <div class="flex items-center py-9 px-7 lg:py-7 lg:pr-4">
                        <div>
                            @if($news_category)
                                <x-category-badge :category="$news_category" variant="outline" class="mb-4.5" />
                            @endif
                            <p class="mb-2.5 text-[0.625rem] font-bold">{{ get_the_date(ABBL_DATE_FORMAT_HUMAN, $news_item) }}</p>
                            <h3 class="mb-3.5 font-headings text-[1.125rem] font-bold">{!! get_the_title($news_item) !!}</h3>
                            <a href="#" class="text-[0.875rem] !underline">&gt; {{ __('See all news', 'abbl') }}</a>
                        </div>
                    </div>
                </li>
            @endforeach
        @endif
    </ul>
    <div>
        <p>The label is voluntary and applies to financial wrappers, whether accounts, funds or insurance-based products, that meet three cumulative criteria: This approach builds on existing products and avoids creating regulatory complexity.</p>
    </div>
</div>
