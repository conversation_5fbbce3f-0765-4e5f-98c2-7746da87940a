@php
    $publications = get_sub_field('related_publications_publications');
@endphp

<div>
    <div class="wysiwyg mb-9">
        <h2>{{ __('Related publication', 'abbl') }}</h2>
    </div>
    <ul>
        @if($publications && count($publications))
            @foreach($publications as $publication)
                @php
                    $publication_category = get_the_terms($publication, TAX_PUBLICATION_CATEGORY)[0] ?? null;
                    $publication_language = get_the_terms($publication, TAX_PUBLICATION_LANGUAGE)[0] ?? null;
                @endphp

                <li>
                    <article class="lg:flex items-center gap-4 py-8 px-7 rounded-lg bg-light-blue">
                        <div class="flex items-center gap-5 mr-auto lg:mr-0 mb-7 lg:mb-0">
                            @if($publication_category)
                                <x-category-badge :category="$publication_category" variant="outline" />
                            @endif
                            @if($publication_language)
                                <x-language-badge :language="$publication_language" />
                            @endif
                        </div>
                        <div class="mb-4 lg:mb-0 lg:mr-auto">
                            <h3 class="font-headings text-[1.125rem] font-bold">{{ get_the_title($publication) }}</h3>
                        </div>
                        <div class="lg:flex items-center gap-5">
                            <a href="{{ abbl_publications_page_url() }}" class="block mb-5 lg:mb-0 !underline">&gt; See all publications</a>
                            <x-link-button
                                href="{{ get_the_permalink($publication) }}"
                                size="sm"
                                class="bg-neon-green"
                            >
                                {{ __('See more', 'abbl') }}
                            </x-link-button>
                        </div>
                    </article>
                </li>
            @endforeach
        @endif
    </ul>
</div>
