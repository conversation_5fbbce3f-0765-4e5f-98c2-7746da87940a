@props([
    'title' => null,
    'badge' => null,
    'bgUrl' => Vite::asset('resources/images/defaults/hero-bg.jpg'),
    'bgUrlDesktop' => null,
    'bgUrlMobile' => null,
    'date' => null,
])

@php
$bgUrlMobile = $bgUrlMobile ?? $bgUrlDesktop ?? $bgUrl;
$bgUrlDesktop = $bgUrlDesktop ?? $bgUrl;
@endphp

<div {{ $attributes->twMerge(['relative flex flex-col justify-center py-24 min-h-[28.125rem] md:min-h-[18.75rem] bg-navy-blue']) }}>
    @if($bgUrlMobile)
        <div class="absolute inset-0 lg:hidden bg-cover bg-center" style="background-image: url('{{ $bgUrlMobile }}')"></div>
    @endif
    @if($bgUrlDesktop)
        <div class="absolute inset-0 hidden lg:block bg-cover bg-center" style="background-image: url('{{ $bgUrlDesktop }}')" ></div>
    @endif
    <div class="absolute inset-0 bg-navy-blue/75"></div>
    <div class="absolute inset-0 left-0 w-[66%] bg-linear-to-r from-navy-blue/75 to-transparent"></div>

    <x-container class="relative">
        @if($badge)
            <div>
                <x-badge class="px-6">{{ $badge }}</x-badge>
            </div>
        @endif
        @if($title)
            <h1 class="mt-5 font-headings text-[1.25rem] font-bold text-neon-green">{!! $title !!}</h1>
        @endif
        <div class="mt-5 max-w-[48rem] text-[0.9375rem] text-white">
            @if($date)
                <p class="text-[0.9375rem] font-bold text-white">{{ $date }}</p>
            @endif
            {{ $slot }}
        </div>
    </x-container>
</div>
