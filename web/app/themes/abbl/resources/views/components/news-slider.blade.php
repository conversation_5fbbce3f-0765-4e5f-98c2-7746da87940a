@php use App\Modules\PostsModule; @endphp
@props([
    'postsPerPage' => 6
])

@php
$news_query = PostsModule::query([
    'posts_per_page' => $postsPerPage,
    // Exclude the current post
    'post__not_in' => [get_the_ID()]
]);
@endphp

<div id="home-news-slider" class="swiper-container with-fade">
    <!-- Slider main container -->
    <div class="swiper">
        <!-- Additional required wrapper -->
        <div class="swiper-wrapper">
            <!-- Slides -->
            @while($news_query->have_posts())
                @php $news_query->the_post(); @endphp

                <div class="swiper-slide">
                    <x-news-card />
                </div>
            @endwhile
        </div>
    </div>

    <div class="swiper-pagination"></div>

    <!-- If we need navigation buttons -->
    <div class="swiper-button-prev">
        <i class="fas fa-square-chevron-left text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
    <div class="swiper-button-next">
        <i class="fas fa-square-chevron-right text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
</div>

@php wp_reset_postdata(); @endphp
