@props([
    'variant' => 'normal',
    'size' => 'normal',
])

@php
    $sizes = [
        'normal' => 'gap-5 h-[3rem] px-9 text-[0.875rem] font-bold select-none',
        'sm' => "relative gap-3 min-h-[2.0625rem] px-5 text-[0.875rem] font-medium after:content-[''] after:block after:absolute after:w-full after:min-h-[3rem] border-2 border-navy-blue/10 hover:border-white select-none",
    ];

    $variants = [
        'normal' => "inline-flex justify-center items-center $sizes[$size] rounded text-navy-blue text-center bg-yellow cursor-pointer transition-all shadow-neon-green hover:shadow-[0_0_20px_var(--color-neon-green)] hover:bg-white -outline-offset-3 outline-navy-blue focus-visible:outline-3 focus-visible:scale-110",
        'outline' => "inline-flex justify-center items-center $sizes[$size] border-2 border-yellow rounded text-navy-blue text-center cursor-pointer transition-all -outline-offset-3 outline-navy-blue focus-visible:outline-3 focus-visible:scale-110",
    ];
@endphp

<button {{ $attributes->twMerge($variants[$variant]) }}>
    {{ $slot }}
</button>
