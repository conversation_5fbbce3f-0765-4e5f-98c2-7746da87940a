@props([
    'authorId' => get_the_author_meta('ID')
])

<div class="overflow-hidden relative md:grid md:grid-cols-[auto_1fr] items-center gap-24 md:py-9 md:px-20 rounded-lg bg-abbl-gradient">
    <div class="z-1 overflow-hidden md:overflow-visible relative flex justify-center md:block md:w-[14rem] py-9 md:py-0">
        <div class="absolute top-[-50%] bottom-0 right-1/2 w-full h-[200%] bg-size-[300%] bg-repeat" style="background-image: url('{{ Vite::asset('resources/images/decorations/diagonals-neon-green.svg') }}')"></div>
        <img class="overflow-hidden relative z-1 w-[14rem] h-[14rem] md:w-auto md:h-auto  rounded-full" src="{{ Vite::asset('resources/images/needs-integration/author-avatar.jpg') }}" alt="">
        <div class="absolute top-0 bottom-0 right-[2.125rem] md:-right-13 w-[0.8125rem] h-full bg-container bg-center bg-no-repeat" style="background-image: url('{{ Vite::asset('resources/images/decorations/arrow-top-yellow.svg') }}')"></div>
    </div>
    <div class="py-11 px-8 md:p-0">
        <p class="text-[1.125rem] font-bold text-neon-green">{{ get_the_author() }}</p>
        @if($job = get_field('author_job', 'user_'.$authorId))
            <p class="text-[1.125rem] text-white">{{ $job }}</p>
        @endif
        <p class="mt-5 text-[0.75rem] text-white"> Published on {{ get_the_date(ABBL_DATE_FORMAT_HUMAN) }}</p>
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-7.5 mt-10">
            @if($linkedin_profile = get_field('author_linkedin_profile', 'user_'.$authorId))
                <a href="{{ $linkedin_profile }}" class="inline-flex items-center h-8 rounded-sm bg-white">
                    <span class="inline-flex items-center h-full px-3 rounded-sm text-white bg-navy-blue">
                        <i class="fab fa-linkedin-in"></i>
                    </span>
                    <span class="px-3 text-[0.875rem] font-bold">LinkedIn Profile</span>
                </a>
            @endif
            <a href="#" class="block text-white !underline">
                See all articles by {{ get_the_author() }}
            </a>
        </div>
    </div>
</div>
