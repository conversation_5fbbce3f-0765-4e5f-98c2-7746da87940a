@if(get_row_layout() == 'text_columns_layout')
    <div class="my-15">
        <x-container>
            <x-flexibles.text-columns />
        </x-container>
    </div>
@endif

@if(get_row_layout() == 'centered_image_layout')
    <div class="my-15">
        <x-container>
            <x-flexibles.centered-image />
        </x-container>
    </div>
@endif

@if(get_row_layout() == 'quote_layout')
    <div class="my-15">
        <x-container class="pt-58 lg:pt-16">
            <x-flexibles.quote />
        </x-container>
    </div>
@endif

@if(get_row_layout() == 'related_publications_layout')
    <div class="my-15">
        <x-container>
            <x-flexibles.related-publications />
        </x-container>
    </div>
@endif

@if(get_row_layout() == 'related_news_layout')
    <div class="my-15">
        <x-container>
            <x-flexibles.related-news />
        </x-container>
    </div>
@endif

@if(get_row_layout() == 'text_and_big_number_layout')
    <div class="my-15">
        <x-container>
            <x-flexibles.text-and-big-number />
        </x-container>
    </div>
@endif

@if (get_row_layout() == 'table_layout')
    <div class="my-15">
        <x-container class="wysiwyg">
            <h2>Comparison of national implementations</h2>
            <table>
                <thead>
                <tr>
                    <td>Lorem ipsum</td>
                    <td>Lorem ipsum</td>
                    <td>Lorem ipsum</td>
                    <td></td>
                </tr>
                </thead>
                <tbody>
                @foreach(range(0, 10) as $i)
                    <tr>
                        <td>Lorem ipsum</td>
                        <td>Lorem ipsum</td>
                        <td>Lorem ipsum</td>
                        <td>
                            <a href="#">
                                <i class="far fa-search"></i>
                            </a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            <p><i>Lorem ipsum dolor sit amet consecetur</i></p>
        </x-container>
    </div>
@endif
