@php use App\Modules\HotTopicsModule; @endphp
@props([
    'postsCount' => 4
])

@php
$hot_topics_query = HotTopicsModule::query([
    'posts_per_page' => $postsCount,
]);

$default_image_url = Vite::asset('resources/images/needs-integration/hot-topics-slider-bg.jpg');
@endphp

<x-section class="relative flex flex-col justify-center min-h-[29.5rem] my-0 md:my-0 pt-27 pb-10 lg:py-12">
    @while($hot_topics_query->have_posts())
        @php
            $hot_topics_query->the_post();

            $image_url = get_the_post_thumbnail_url() ?? $default_image_url;
        @endphp

        {{-- TODO: Add mobile image --}}
        <div class="@if($hot_topics_query->current_post === 0) active @endif home-topics-slider-image absolute inset-0 bg-cover bg-y-top bg-x-center" style="background-image: url('{{ get_the_post_thumbnail_url() ?: $default_image_url }}')"></div>
    @endwhile

    @php $hot_topics_query->rewind_posts(); @endphp

    <div class="absolute inset-0 bg-navy-blue/75"></div>
    <div class="absolute inset-0 left-0 w-[66%] bg-linear-to-r from-navy-blue/75 to-transparent"></div>

    <div id="home-hot-topics-slider" class="swiper-container with-fade">
        <x-container class="relative">
            <!-- Slider main container -->
            <div class="swiper">
                <!-- Additional required wrapper -->
                <div class="swiper-wrapper">
                    @php $i = 0; @endphp

                    @while($hot_topics_query->have_posts())
                        @php
                            $hot_topics_query->the_post();

                            $category = get_the_terms(get_the_ID(), TAX_HOT_TOPIC_CATEGORY)[0] ?? null;
                        @endphp

                        <div class="swiper-slide">
                            <div>
                                <article class="flex flex-col px-9 lg:px-0 lg:pr-[29rem]">
                                    <div class="mb-8">
                                        @if ($category)
                                            <x-category-badge :category="$category" />
                                        @endif
                                    </div>
                                    <div class="mt-auto">
                                        <h3 class="mb-4 text-[1.25rem] font-headings font-bold text-neon-green">{{ get_the_title() }}</h3>
                                        <div class="mb-6 md:mb-9 text-white">
                                            <p class="line-clamp-3">{{ get_the_excerpt() }}</p>
                                        </div>
                                        <x-link-button class="w-full md:w-auto" href="{{ get_the_permalink() }}">
                                            {{ __('Explore this topic', 'abbl') }}
                                        </x-link-button>
                                    </div>
                                </article>
                            </div>
                        </div>

                        @php $i++; @endphp
                    @endwhile

                    @php
                        wp_reset_postdata();
                    @endphp
                </div>
            </div>

            <div class="swiper-pagination lg:!hidden mt-13"></div>

            <!-- If we need navigation buttons -->
            <div class="swiper-button-prev hidden lg:block lg:!left-[0.5rem]">
                <i class="fas fa-square-chevron-left text-yellow lg:text-neon-green"></i>
            </div>
            <div class="swiper-button-next hidden lg:block lg:!right-[0.5rem]">
                <i class="fas fa-square-chevron-right text-yellow lg:text-neon-green"></i>
            </div>

            {{-- START LINES UNDER BUTTON --}}
            <div
                class="z-10 absolute left-[5rem] bottom-[-6.55rem] w-[12.125rem] h-[3rem] hidden lg:block bg-size-[100%] point-events-none select-none"
                style="background-image: url('{{ Vite::asset('resources/images/decorations/diagonals-white.png') }}')"
            ></div>
            {{-- END LINES UNDER BUTTON --}}

            {{-- START YELLOW CANDLES --}}
            <img
                src="{{ Vite::asset('resources/images/decorations/yellow-candles.svg') }}"
                class="absolute -right-[22.4375rem] -bottom-[12.775rem] w-[254px] h-[293px] hidden lg:block point-events-none bg-center select-none"
                alt=""
            >
            {{-- END YELLOW CANDLES --}}

            {{-- START CUT GREEN ARROW --}}
            <img
                class="z-10 absolute left-[61%] bottom-[-105px] w-[32px] h-[134px] hidden lg:block point-events-none bg-fit bg-center select-none"
                src="{{ Vite::asset('resources/images/decorations/arrow-green-cut.png') }}"
                alt=""
            >
            {{-- END CUT GREEN ARROW --}}
        </x-container>
    </div>

    <nav class="z-10 absolute inset-0 hidden lg:block pointer-events-none">
        <x-container class="flex justify-end items-center h-full">
            <div class="w-[21.5rem] p-6 rounded-lg bg-white pointer-events-auto">
                <h2 aria-hidden="true" class="pb-4 text-[1.125rem] font-headings font-bold">{{ __("What’s trending", 'abbl') }}</h2>

                @php $i = 0; @endphp

                <ul id="slider-hot-topics-list" aria-hidden="true">
                    @while($hot_topics_query->have_posts())
                        @php
                            $hot_topics_query->the_post();
                        @endphp

                        <li
                            data-index="{{ $hot_topics_query->current_post }}"
                            @class([
                                'flex items-center py-3 border-t border-neon-green last-of-type:border-b text-[0.8125rem] cursor-pointer',
                                'active' => $i === 0,
                            ])
                        >
                            <i class="fas fa-chevron-right shrink-0 grow-0 overflow-hidden w-0 text-[1.25rem] text-neon-green"></i>
                            <p class="line-clamp-2">{{ get_the_title() }}</p>
                        </li>

                        @php $i++; @endphp
                    @endwhile

                    @php wp_reset_postdata(); @endphp
                </ul>
                <div class="mt-7">
                    <x-link-button href="{{ get_post_type_archive_link(CPT_HOT_TOPIC) }}" size="sm" class="bg-neon-green">
                        {{ __('See all hot topics', 'abbl') }}
                    </x-link-button>
                </div>
            </div>
        </x-container>
    </nav>
</x-section>
