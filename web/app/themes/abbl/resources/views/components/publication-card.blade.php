@props([
    'featured' => get_field('is_featured', get_the_ID()),
    'isPdf' => false,
    'reducedContent' => false,
])

@php
$category = get_the_terms(get_the_ID(), TAX_PUBLICATION_CATEGORY)[0] ?? null;
$languages = get_the_terms(get_the_ID(), TAX_PUBLICATION_LANGUAGE) ?: [];
$partners = range(0, rand(0, 3)); // TODO: Change this
@endphp

<article {{ $attributes->twMerge('flex flex-col px-9 md:px-6 py-6 rounded-lg bg-white' . ($featured ? ' glow-neon-green border border-neon-green' :  '')) }}>
    <div class="flex justify-between items-center gap-3 mb-4">
        <div class="flex items-center gap-3">
            @if($category)
                <x-category-badge  class="lg:min-w-[7.5rem]" variant="outline" :category="$category" />
            @endif
            @if($featured)
                <x-badge class="text-neon-green text-[0.75rem] font-bold bg-navy-blue">Featured</x-badge>
            @endif
        </div>
        <div class="flex items-center gap-3">
            @foreach($languages as $language)
                <x-language-badge :language="$language"/>
            @endforeach
        </div>
    </div>
    <h3 @class([
        'text-[1.125rem] font-headings font-bold',
        'mb-9' => !$reducedContent,
        'mb-4' => $reducedContent,
    ])>{{ get_the_title() }}</h3>
    @if(!$reducedContent)
        <div class="mb-2">
            <p class="text-[0.6875rem] font-medium">{{ __('Published on') }} {{ get_the_date('m/d/Y') }}</p>
        </div>
        <div class="mb-3 text-[0.875rem]">
           {!! get_the_excerpt() !!}
        </div>
    @endif
    <div class="mt-auto">
        @if($partners && count($partners) && !$reducedContent)
            <div class="mb-2 md:mb-8 text-[0.5625rem]">
                <p>
                    @if(count($partners) > 1)
                        {!! sprintf(__('In partnership with %s organizations', 'abbl'), count($partners)) !!}
                    @else
                        {!! __('In partnership with 1 organization', 'abbl') !!}
                    @endif
                </p>
            </div>
        @endif
        <div>
            <x-link-button href="{{ get_the_permalink() }}" size="sm" class="bg-neon-green">
                @if($isPdf)
                    <i class="far fa-file-download"></i> {{ __('Download PDF', 'abbl') }}
                @else
                    {{ __('See more', 'abbl') }}
                @endif
            </x-link-button>
        </div>
    </div>
</article>
