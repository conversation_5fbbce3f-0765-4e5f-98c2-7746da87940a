@php
    $contract_type = get_the_terms(get_the_ID(), TAX_JOB_OFFER_CONTRACT_TYPE)[0] ?? null;
    $author_id = get_the_author_meta('ID');
    $publication_date = Carbon\Carbon::createFromTimestamp(get_the_date('U'));

    $is_abbl_job = get_field('is_abbl_job');
    $is_new = $publication_date->diffInDays(Carbon\Carbon::now()) < 7;
@endphp

<div class="space-y-4 p-4 bg-gray-200 @if($is_abbl_job) border-black border-4 @endif">
    <div class="flex gap-2">
        @if($contract_type)
            <span class="block px-1 py-0.5 text-xs font-semibold bg-gray-300">{{ $contract_type->name }}</span>
        @endif
        @if($is_abbl_job)
            <span
                class="block px-1 py-0.5 text-xs font-semibold text-white bg-gray-500">{{ __('ABBL TEAM', 'abbl') }}</span>
        @endif
        @if($publication_date->diffInDays(Carbon\Carbon::now()) < 7)
            <span class="block px-1 py-0.5 text-xs font-semibold text-white bg-gray-400">{{ __('NEW!', 'abbl') }}</span>
        @endif
    </div>
    <h3 class="text-2xl font-bold">{{ get_the_title() }}</h3>
    <div>
        @if ($logo = get_field('company_logo', "user_$author_id"))
            <img class="max-w-[6.25rem] mx-auto" src="{{ $logo['url'] }}" alt="Logo {{ $logo['alt'] }}">
        @endif
    </div>
    <div>{{ get_the_excerpt() }}</div>
    @if ($publication_date)
        <div>
            <p class="text-sm opacity-60">{{ __('Published on:', 'abbl') }} {{ $publication_date->format(ABBL_DATE_FORMAT_HUMAN) }}</p>
        </div>
    @endif
    <a class="inline-block px-4 py-2 bg-gray-400" href="{{ get_the_permalink() }}">{{ __('View offer', 'abbl') }}</a>
</div>
