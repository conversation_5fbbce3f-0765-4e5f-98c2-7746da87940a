@php use App\Modules\EventsModule; @endphp
@props([
    'postsPerPage' => 6
])

@php
    $events_query = EventsModule::query([
        'posts_per_page' => $postsPerPage,
    ]);
@endphp

<div data-events-slider class="swiper-container with-fade">
    <!-- Slider main container -->
    <div class="swiper">
        <!-- Additional required wrapper -->
        <div class="swiper-wrapper">
            <!-- Slides -->
            @while($events_query->have_posts())
                @php $events_query->the_post(); @endphp

                <div class="swiper-slide">
                    <x-event-card class="h-full" />
                </div>
            @endwhile
        </div>
    </div>

    <div class="swiper-pagination"></div>

    <!-- If we need navigation buttons -->
    <div class="swiper-button-prev">
        <i class="fas fa-square-chevron-left text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
    <div class="swiper-button-next">
        <i class="fas fa-square-chevron-right text-[1.25rem] lg:text-[2rem] text-navy-blue lg:text-neon-green"></i>
    </div>
</div>

@php wp_reset_postdata(); @endphp
