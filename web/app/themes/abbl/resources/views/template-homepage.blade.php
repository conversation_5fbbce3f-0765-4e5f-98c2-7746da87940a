{{--
  Template Name: Global homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <section class="relative pt-6 pb-[5.625rem] md:pt-18 md:pb-34 bg-abbl-gradient-grain">
            <x-container class="relative">
                {{-- START GREEN CANDLES --}}
                <img
                    class="absolute left-[-213px] top-[290px] hidden lg:block w-[126px] h-[147px] select-none"
                    src="{{ Vite::asset('resources/images/decorations/green-candles.png') }}"
                    alt=""
                >
                {{-- E<PERSON> GREEN CANDLES --}}

                {{-- START RIGHT WHITE ARROW --}}
                <img
                    class="absolute right-[-80px] top-[84px] hidden lg:block w-[15px] h-[169px] select-none"
                    src="{{ Vite::asset('resources/images/decorations/home-arrow-white.png') }}"
                    alt=""
                >
                {{-- END RIGHT WHITE ARROW --}}

                {{-- START LEFT WHITE ARROW --}}
                <img
                    class="absolute left-[-86px] bottom-[-94px] hidden lg:block w-[15px] h-[169px] select-none"
                    src="{{ Vite::asset('resources/images/decorations/home-arrow-white.png') }}"
                    alt=""
                >
                {{-- END LEFT WHITE ARROW --}}

                {{-- START CREDIT CARD --}}
                <img
                    class="absolute right-[-312px] bottom-[22px] hidden lg:block w-[112px] h-[142px] select-none"
                    src="{{ Vite::asset('resources/images/decorations/home-credit-card.png') }}"
                    alt=""
                >
                {{-- END CREDIT CARD --}}

                <div class="pt-4 pb-6 px-8 md:px-25 md:pt-13 md:pb-10 rounded-lg bg-navy-blue/50">
                    @while(have_rows('hero_section'))
                        @php the_row(); @endphp

                        <div class="mb-5 md:mb-15 text-center">
                            @if($title = get_sub_field('title'))
                                <h1 class="md:mb-9 font-headings leading-[1.25] text-[1.25rem] md:text-[1.75rem] font-bold text-neon-green">
                                    {!! nl2br($title) !!}
                                </h1>
                            @endif
                            @if($subtitle = get_sub_field('subtitle'))
                                <p class="hidden md:block font-semibold text-white">
                                    {!! nl2br($subtitle) !!}
                                </p>
                            @endif
                        </div>
                    @endwhile

                    <div class="flex justify-around md:hidden">
                        <div class="flex gap-4 p-2 mb-7 rounded-full bg-white/75">
                            @php
                            $buttons = [
                                [
                                    'aria-label' => __('View professionals panel', 'abbl'),
                                    'label' => __('Professional', 'abbl'),
                                    'status' => 'active',
                                    'switcher-button' => 'professionals',
                                ],
                                [
                                    'aria-label' => __('View consumers panel', 'abbl'),
                                    'label' => __('Consumer', 'abbl'),
                                    'status' => 'inactive',
                                    'switcher-button' => 'consumers',
                                ],
                            ];
                            @endphp

                            {{-- TODO: Maybe avoid the small layout shift because of the font ? --}}
                            @foreach($buttons as $button)
                                <button
                                    aria-label="{{ $button['aria-label'] }}"
                                    data-switcher-button="{{ $button['switcher-button'] }}"
                                    data-active="{{ $button['status'] }}"
                                    class="py-3 px-4 rounded-full bg-transparent data-[active=active]:font-bold data-[active=active]:text-white data-[active=active]:bg-navy-blue transition-colors"
                                >{{ $button['label'] }}</button>
                            @endforeach
                        </div>
                    </div>

                    <div class="overflow-hidden md:grid grid-cols-2 gap-5 mb-6 md:mb-10 rounded-lg md:rounded-none">
                        <div class="flex md:contents data-[zone=consumers]:-translate-x-[100%] transition-transform" data-zone="professionals">
                            @foreach(['professionals' => 'professional_section', 'consumers' => 'consumer_section'] as  $name => $section)
                                @while(have_rows($section))
                                    @php the_row(); @endphp

                                    <article
                                        @class([
                                            'overflow-hidden shrink-0 w-full flex flex-col rounded-none',
                                            'md:rounded-l-lg' => $section === 'professional_section',
                                            'md:rounded-r-lg' => $section === 'consumer_section',
                                        ])
                                    >
                                        <div
                                            @class([
                                                'flex flex-col justify-center items-center pt-4 pb-6 md:pt-12 md:pb-10 px-4 md:px-8 text-center',
                                                'text-white bg-navy-blue' => $section === 'professional_section',
                                                'text-navy-blue bg-neon-green' => $section === 'consumer_section',
                                            ])
                                        >
                                            @if($image = get_sub_field('image'))
                                                <img class="md:order-3 block w-full h-[20vw] md:h-[13.75rem] mb-2 md:mb-0 object-fit object-center" src="{{ $image['url'] }}" alt="{{ $image['alt'] }}">
                                            @endif
                                            @if($title = get_sub_field('title'))
                                                <h2 class="md:mb-1 text-[1.75rem] md:text-[1.25rem] font-bold">{{ $title }}</h2>
                                            @endif
                                            @if($description = get_sub_field('description'))
                                                <p class="md:mb-8">{!! $description !!}</p>
                                            @endif
                                        </div>
                                        <div class="grow flex flex-col justify-center items-center gap-6 md:gap-8 text-center py-5 md:pt-8 md:pb-11 px-4 md:px-8 bg-white">
                                            @if($button_link = get_sub_field('button_link'))
                                                <x-link-button id="home-{{ $name }}-button" class="max-lg:px-4 max-lg:gap-4 md:order-2 md:mt-auto" href="{{ $button_link['url'] }}" target="{{ $button_link['target'] ?? '_self' }}">
                                                    <span>{{ $button_link['title'] }}</span>
                                                    <i class="far fa-arrow-right text-[1.5rem]"></i>
                                                </x-link-button>
                                            @endif
                                            @if($detailed_description = get_sub_field('detailed_description'))
                                                <p class="text-[0.875rem]">{!! nl2br($detailed_description) !!}</p>
                                            @endif
                                        </div>
                                    </article>
                                @endwhile
                            @endforeach
                        </div>
                    </div>

                    @while(have_rows('bottom_message'))
                        @php the_row(); @endphp

                        <aside class="flex justify-center items-center gap-6 text-white">
                            <i class="far fa-arrow-right-arrow-left text-[1.5rem] text-neon-green"></i>
                            @if($message = get_sub_field('message'))
                                <p class="font-medium">{{ $message }}</p>
                            @endif
                        </aside>
                    @endwhile
                </div>
            </x-container>
        </section>
    @endwhile
@endsection

@push('head-first-scripts')
    <script>
        const userType = window.localStorage.getItem('abblUserType');

        if (userType === 'professional') {
            window.location = '{{ get_permalink(ID_HOMEPAGE_PRO) }}';
        }

        if (userType === 'consumer') {
            window.location = '{{ get_permalink(ID_HOMEPAGE_CONSUMERS) }}';
        }
    </script>
@endpush

@push('footer-scripts')
    <script>
        const professionalsButton = document.querySelector('#home-professionals-button');
        const consumersButton = document.querySelector('#home-consumers-button');

        professionalsButton.addEventListener('click', (e) => {
            e.preventDefault()

            window.localStorage.setItem('abblUserType', 'professional');

            window.location = professionalsButton.href;
        });

        consumersButton.addEventListener('click', (e) => {
            e.preventDefault()

            window.localStorage.setItem('abblUserType', 'consumer');

            window.location = consumersButton.href;
        });
    </script>
@endpush

@push('footer-scripts')
    <script>
        const switcherButtons = Array.from(document.querySelectorAll('[data-switcher-button]'));
        const zone = document.querySelector('[data-zone]');

        switcherButtons.forEach((el) => {
            el.addEventListener('click', () => {
                switcherButtons.forEach((el) => el.dataset.active = 'inactive');
                el.dataset.active = 'active';

                zone.dataset.zone = el.dataset.switcherButton;
            });
        });
    </script>
@endpush
