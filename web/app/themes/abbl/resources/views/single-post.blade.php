@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
        the_post();

        $category = get_the_terms(get_the_ID(), 'category')[0] ?? null;

        $has_takeaways = get_field('has_takeaways');
        @endphp

        <x-flexibles.hero
            :badge="$category?->name ?? null"
            :title="get_the_title()"
            :date="get_the_date(ABBL_DATE_FORMAT_HUMAN)"
            :bg-url="get_the_post_thumbnail_url()"
        >
            <p>{{ get_the_excerpt() }}</p>
        </x-flexibles.hero>

        <aside class="bg-abbl-gradient-grain">
            <div class="py-14">
                <x-container>
                    <div class="flex justify-between mb-6 text-white">
                        <x-back-button href="#">
                            {{ __('Back to news', 'abbl') }}
                        </x-back-button>
                        <x-share-page />
                    </div>
                    @if($has_takeaways)
                        <div class="pt-8 pb-10 px-11 bg-navy-blue/50 rounded-lg">
                            <h2 class="mb-4 font-headings text-[1.25rem] font-bold text-neon-green">
                                {{ __('Key takeaways', 'abbl') }}
                            </h2>
                            <div class="max-w-[49.625rem] text-white [&>ul]:list-disc [&>ul]:list-inside">
                                {!! get_field('takeaways_content') !!}
                            </div>
                        </div>
                    @endif
                </x-container>
            </div>
        </aside>

        <x-section>
            <div>
                @while(have_rows('sections'))
                    @php the_row(); @endphp

                    <x-flexible-content />
                @endwhile

                <div class="mt-20 mb-11">
                    <x-container>
                        <div class="mb-6 wysiwyg">
                            <h2>Further reading</h2>
                        </div>
                        <div class="px-5 py-4 rounded bg-light-blue [&_a]:font-bold [&_a]:!underline">
                            <p>Read the official Ministry of Finance press release and download the full termsheet: <a href="#">Finance Europe – Luxembourg Ministry of Finance</a></p>
                        </div>
                    </x-container>
                </div>

                <div class="mb-15">
                    <x-container>
                        <x-post-author-info />
                    </x-container>
                </div>

                <x-container>
                    <x-flexibles.newsletter />
                </x-container>
            </div>
        </x-section>

        <aside class="py-10 lg:py-38 bg-abbl-gradient-grain">
            <x-container>
                <h2 class="mb-12 font-headings text-[1.75rem] font-bold text-white">
                    {{ __('More on this topic', 'abbl') }}
                </h2>
                <x-news-slider />
                <div class="mt-5 lg:mt-1.5">
                    <x-link-button href="#" class="md:inline-flex w-full md:w-auto">{{ __('See all related articles', 'abbl') }}</x-link-button>
                </div>
            </x-container>
        </aside>
    @endwhile
@endsection
