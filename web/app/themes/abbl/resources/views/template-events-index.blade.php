@php use App\Modules\EventsModule; @endphp

{{--
  Template Name: Events index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
            the_post();
        @endphp

        <x-flexibles.hero
            title="Discover upcoming conferences, meetings and events shaping Luxembourg’s financial sector."
            :bg-url-mobile="get_field('hero_mobile_image')['url'] ?? false"
            :bg-url-desktop="get_field('hero_desktop_image')['url'] ?? false"
        >
            {{-- TODO: integration with CMS --}}
        </x-flexibles.hero>

        <section class="space-y-8 pt-10 pb-15 lg:py-32 bg-abbl-gradient-grain">
            <x-container>
                <form method="GET">
                    <x-archive-filters>
                        @php
                            $organizers = get_terms(['taxonomy' => TAX_EVENT_ORGANIZER, 'hide_empty' => true]);
                            $event_categories = get_terms(['taxonomy' => TAX_EVENT_CATEGORY, 'hide_empty' => true]);
                            $event_dates = abbl_get_event_dates();
                        @endphp

                        <x-select name="{{ TAX_EVENT_ORGANIZER }}" onchange="this.closest('form').submit();">
                            <option value="">{{ __('Organizer', 'abbl') }}</option>
                            @foreach ($organizers as $organizer)
                                <option
                                    value="{{ $organizer->slug }}"
                                    @if(get_query_var(TAX_EVENT_ORGANIZER) == $organizer->slug) selected @endif
                                >
                                    {{ $organizer->name }}
                                </option>
                            @endforeach
                        </x-select>

                        <x-select name="{{ TAX_EVENT_CATEGORY }}" onchange="this.closest('form').submit();">
                            <option value="">{{ __('Event category', 'abbl') }}</option>
                            @foreach ($event_categories as $event_category)
                                <option
                                    value="{{ $event_category->slug }}"
                                    @if(get_query_var(TAX_EVENT_CATEGORY) == $event_category->slug) selected @endif
                                >
                                    {{ $event_category->name }}
                                </option>
                            @endforeach
                        </x-select>

                        <x-select name="event-date" onchange="this.closest('form').submit();">
                            <option value="">{{ __('Event date', 'abbl') }}</option>
                            @foreach ($event_dates as $event_date)
                                <option
                                    value="{{ $event_date->format('m-Y') }}"
                                    @if(get_query_var('event-date') == $event_date->format('m-Y')) selected @endif
                                >
                                    {{ $event_date->format('F Y') }}
                                </option>
                            @endforeach
                        </x-select>

                        <x-select name="event-date" onchange="this.closest('form').submit();">
                            <option value="">{{ __('All events', 'abbl') }}</option>
                            @foreach ($event_dates as $event_date)
                                <option
                                    value="{{ $event_date->format('m-Y') }}"
                                    @if(get_query_var('event-date') == $event_date->format('m-Y')) selected @endif
                                >
                                    {{ $event_date->format('F Y') }}
                                </option>
                            @endforeach
                        </x-select>

                        <x-select name="event-date" onchange="this.closest('form').submit();">
                            <option value="">{{ __('Sort by', 'abbl') }}</option>
                            @foreach ($event_dates as $event_date)
                                <option
                                    value="{{ $event_date->format('m-Y') }}"
                                    @if(get_query_var('event-date') == $event_date->format('m-Y')) selected @endif
                                >
                                    {{ $event_date->format('F Y') }}
                                </option>
                            @endforeach
                        </x-select>

                        <x-button class="hidden lg:inline-flex gap-6 px-4 ml-auto border-2 border-neon-green bg-white">
                            <span>{{ __('Clear', 'abbl') }}</span>
                            <i class="far fa-times-circle"></i>
                        </x-button>
                        <x-button class="lg:hidden ml-auto w-full bg-neon-green">{{ __('Clear all filters', 'abbl') }}</x-button>
                    </x-archive-filters>
                </form>
            </x-container>

            <x-container class="mt-30">
                <div class="mb-12">
                    <h2 class="mb-8 font-headings text-[1.75rem] font-bold text-white">Upcoming events</h2>
                    <x-badge variant="outline" class="relative top-0.5 border-neon-green bg-neon-green text-navy-blue">{{ date('F Y') }}</x-badge>
                </div>

                <div class="space-y-8 mb-8 lg:mb-16">
                    @php
                        $events_query = EventsModule::query([
                            'posts_per_page' => -1,
                        ]);
                    @endphp

                    @if ($events_query->have_posts())
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @while($events_query->have_posts())
                                @php $events_query->the_post(); @endphp

                                <x-event-card @class(['lg:col-span-2' => $events_query->current_post === 0 && get_field('is_featured')]) />
                            @endwhile
                        </div>
                    @endif
                </div>

                <div class="grid grid-flow-row justify-stretch lg:grid-flow-col lg:justify-center gap-5">
                    <x-link-button href="#" class="w-full lg:w-auto text-white bg-navy-blue/75 hover:text-navy-blue">
                        <i class="fas fa-angles-down"></i>
                        <span>{{ __('Load more events') }}</span>
                    </x-link-button>
                    <x-link-button href="#" class="w-full lg:w-auto text-white bg-navy-blue/75 hover:text-navy-blue">{{ __('View past events', 'abbl') }}</x-link-button>
                </div>
            </x-container>
        </section>

        <section class="py-26">
            <x-container>
                <x-flexibles.newsletter />
            </x-container>
        </section>
    @endwhile
@endsection
