@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
        the_post();

        $category = get_the_terms(get_the_ID(), 'category')[0] ?? null;

        $has_takeaways = get_field('has_takeaways');
        @endphp

        <x-flexibles.hero
            :badge="$category?->name ?? null"
            :title="get_the_title()"
            :date="get_the_date(ABBL_DATE_FORMAT_HUMAN)"
            :bg-url-mobile="get_field('hero_mobile_image')['url'] ?? false"
            :bg-url-desktop="get_field('hero_desktop_image')['url'] ?? false"
        >
            <p>{{ get_the_excerpt() }}</p>
        </x-flexibles.hero>

        <aside class="bg-abbl-gradient-grain">
            <div class="pt-14">
                <x-container>
                    <div class="flex justify-between px-6 lg:px-0 mb-4 lg:mb-13 text-white">
                        <x-back-button href="#">
                            {{ __('Back to publications', 'abbl') }}
                        </x-back-button>
                        <x-share-page />
                    </div>
                    <div class="pt-8 px-8 pb-10 md:pt-12 md:px-25 md:pb-14 rounded-t-lg bg-navy-blue/50">
                        <div class="mb-12">
                            <h2 class="mb-4 text-[1.25rem] font-headings font-bold text-neon-green">Executive summary</h2>
                            <div class="wysiwyg text-white">
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam pretium, augue sit amet lacinia vehicula, nulla orci rhoncus elit, at lobortis turpis quam a metus. Aenean vel quam vel nunc suscipit porttitor ut a eros.</p>
                                <p>Duis porttitor urna ipsum, a vehicula orci pretium et. Phasellus elementum justo tellus, facilisis dictum risus convallis in. Proin sed orci odio. Morbi non dui nisl. Aenean dictum nisl vel molestie fringilla.</p>
                            </div>
                        </div>
                        <div class="flex flex-col md:flex-row gap-3 md:gap-5 md:p-5 md:rounded-lg md:bg-navy-blue/25">
                            @php
                            $links = [
                                [
                                    'label' => 'Contact us',
                                    'icon' => false,
                                ],
                                [
                                    'label' => 'Share on linkedin',
                                    'icon' => 'far fa-share-nodes'
                                ]
                            ];
                            @endphp

                            <x-link-button href="#" class="w-full md:w-auto px-5 mr-0 md:mr-auto outline-neon-green">
                                <i class="far fa-file-download"></i>
                                <span>Download PDF</span>
                            </x-link-button>
                            @foreach($links as $link)
                                <x-link-button href="#" class="w-full md:w-auto px-5 border-white text-white font-medium hover:bg-white hover:text-navy-blue outline-neon-green" variant="outline">
                                    @if($link['icon'] ?? false) <i class="{{ $link['icon'] }} text-[1.25rem]"></i> @endif
                                    <span>{{ $link['label'] }}</span>
                                </x-link-button>
                            @endforeach
                        </div>
                    </div>
                </x-container>
            </div>
        </aside>

        <x-section>
            <div>
                @while(have_rows('sections'))
                    @php the_row(); @endphp

                    <x-flexible-content />
                @endwhile

                <x-container>
                    <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 md:gap-7 px-8 py-7 rounded-lg text-white bg-abbl-gradient">
                        <div class="pb-4 md:pb-0 md:pr-7 border-b-2 md:border-b-0 md:border-r-2 border-white">
                            <p>Published by: <strong>ABBL</strong></p>
                            <p>Published on: <strong>{{ get_the_date(ABBL_DATE_FORMAT_HUMAN) }}</strong></p>
                        </div>
                        <div class="pb-4 md:pb-0 md:pr-4 border-b-2 md:border-b-0 md:border-r-2 border-white">
                            <p>Status: <strong>Public</strong></p>
                            <p>Type: <strong>Annual report</strong></p>
                        </div>
                        <div>
                            <p>Format: <strong>PDF</strong></p>
                            <p>Language: <strong>French</strong></p>
                        </div>
                        <x-link-button href="#" class="w-full md:w-auto px-5 ml-0 md:ml-auto">
                            <i class="far fa-file-download"></i>
                            <span>Download PDF</span>
                        </x-link-button>
                    </div>
                </x-container>
            </div>
        </x-section>

        <aside class="py-10 lg:py-38 bg-abbl-gradient-grain">
            <x-container>
                <h2 class="relative z-10 mb-12 font-headings text-[1.75rem] font-bold text-white">
                    {{ __('More on this topic', 'abbl') }}
                </h2>
                <x-publications-slider :reducedContent="true" />
                <div class="relative z-10 mt-5 lg:mt-1.5">
                    <x-link-button href="#" class="md:inline-flex w-full md:w-auto">
                        {{ __('See all related articles', 'abbl') }}
                    </x-link-button>
                </div>
            </x-container>
        </aside>

        <aside class="py-10 lg:py-25">
            <x-container>
                <x-flexibles.newsletter title="Don’t miss our next reports. Subscribe to the ABBL newsletter." />
            </x-container>
        </aside>
    @endwhile
@endsection
