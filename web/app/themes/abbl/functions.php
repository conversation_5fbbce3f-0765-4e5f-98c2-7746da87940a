<?php

use Roots\Acorn\Application;

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| our theme. We will simply require it into the script here so that we
| don't have to worry about manually loading any of our classes later on.
|
*/

if (! file_exists($composer = __DIR__.'/vendor/autoload.php')) {
    wp_die(__('Error locating autoloader. Please run <code>composer install</code>.', 'abbl'));
}

require $composer;

/*
|--------------------------------------------------------------------------
| Register The Bootloader
|--------------------------------------------------------------------------
|
| The first thing we will do is schedule a new Acorn application container
| to boot when WordPress is finished loading the theme. The application
| serves as the "glue" for all the components of Laravel and is
| the IoC container for the system binding all of the various parts.
|
*/

Application::configure()
    ->withProviders([
        App\Providers\ThemeServiceProvider::class,
    ])
    ->boot();

/*
|--------------------------------------------------------------------------
| Register Sage Theme Files
|--------------------------------------------------------------------------
|
| Out of the box, Sage ships with categorically named theme files
| containing common functionality and setup to be bootstrapped with your
| theme. Simply add (or remove) files from the array below to change what
| is registered alongside Sage.
|
*/

collect(['setup', 'filters'])
    ->each(function ($file) {
        if (! locate_template($file = "app/{$file}.php", true, true)) {
            wp_die(
                /* translators: %s is replaced with the relative file path */
                sprintf(__('Error locating <code>%s</code> for inclusion.', 'abbl'), $file)
            );
        }
    });

const CPT_POST = 'post';
const CPT_EVENT = 'event';
const CPT_PERSON = 'person';
const CPT_COMPANY = 'company';
const CPT_TEAM_MEMBER = 'team-member';
const CPT_ABBL_MEMBER = 'abbl-member';
const CPT_HOT_TOPIC = 'hot-topic';
const CPT_PUBLICATION = 'publication';
const CPT_JOB_OFFER = 'job-offer';

const TAX_POST_CONTENT_TYPE = 'post-content-type';

const TAX_EVENT_CATEGORY = 'event-category';
const TAX_EVENT_ORGANIZER = 'event-organizer';
const TAX_EVENT_VENUE = 'event-venue';
const TAX_EVENT_FORMAT = 'event-format'; // In person, hybrid, online
const TAX_EVENT_LANGUAGE = 'event-language';

const TAX_ABBL_MEMBER_CATEGORY = 'abbl-member-category';

const TAX_TEAM_MEMBER_TEAM = 'team-member-team';

const TAX_HOT_TOPIC_CATEGORY = 'hot-topic-category';

const TAX_PUBLICATION_CATEGORY = 'publication-category';
const TAX_PUBLICATION_LANGUAGE = 'publication-language';

const TAX_JOB_OFFER_COMPANY = 'job-company';
const TAX_JOB_OFFER_CONTRACT_TYPE = 'job-offer-contract-type';
const TAX_JOB_OFFER_FIELD = 'job-offer-field';

const TAX_SHARED_SITE_SECTION = 'site-section';

const ABBL_DATE_FORMAT = 'm.d.Y';
const ABBL_DATE_FORMAT_HUMAN = 'd F Y';
const ABBL_TIME_FORMAT = 'H:i';

const ID_HOMEPAGE_PRO = 57;
const ID_HOMEPAGE_CONSUMERS = 61;

function abbl_google_map_script_tag() : string {
    $query_string = http_build_query([
        'key' => env('GOOGLE_MAPS_KEY'),
        'callback' => 'Function.prototype',
    ]);

    return "<script src='https://maps.googleapis.com/maps/api/js?$query_string'></script>";
}

function abbl_acf_to_carbon($value) : ?Carbon\Carbon {
    if (empty($value)) {
        return null;
    }

    return \Carbon\Carbon::createFromFormat('d/m/Y H:i A', $value);
}

/**
 * @return array<Carbon\Carbon>
 */
function abbl_get_publication_dates(string $post_type = 'post'): array {
    global $wpdb;

    $publication_dates = $wpdb->get_results("
        SELECT
            MONTH(post_date) AS month,
            YEAR(post_date) AS year,
            COUNT(*) AS count
        FROM
            $wpdb->posts
        WHERE
            post_type = 'post' AND
            post_status = 'publish'
        GROUP BY
            year,
            month
        ORDER BY
            year DESC,
            month DESC;
    ");

    return array_map(fn ($date) => \Carbon\Carbon::createFromDate(year: $date->year, month: $date->month), $publication_dates);
}

/**
 * @return array<\Carbon\Carbon>
 */
function abbl_get_event_dates() {
    $event_ids = get_posts([
        'post_type' => CPT_EVENT,
        'fields' => 'ids',
        'posts_per_page' => -1,
    ]);

    $dates = [];

    foreach ($event_ids as $id) {
        if ($date = get_field('start_datetime', $id)) {
            $dates[] = abbl_acf_to_carbon($date);
        }
    }

    return $dates;
}

function abbl_on_pro_section() {
    // TODO: Use real data from the CMS
    return !str_contains($_SERVER['REQUEST_URI'], '/consumers/');
}

function abbl_on_consumer_section() {
    return !abbl_on_pro_section();
}

function abbl_get_job_posters() {
    return get_users([
        'role' => 'job_poster',
    ]);
}

function abbl_user_is_in_abbl_team($user_id) {
    return $user_id === 1;
}

function abbl_professionals_home_url() {
    return get_permalink(get_field('page_professionals_home', 'options'));
}

function abbl_consumers_home_url() {
    return get_permalink(get_field('page_consumers_home', 'options'));
}

function abbl_search_page_url() {
    return get_permalink(get_field('page_search', 'options'));
}

function abbl_become_a_member_page_url() {
    return get_permalink(get_field('page_become_a_member', 'options'));
}

function abbl_publications_page_url() {
    return get_permalink(get_field('page_publications', 'options'));
}
