<?php

namespace App\Modules;

class PostsModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        // We'll keep this empty for now.
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = 'post';
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        $ignore_featured = $query['ignore_featured'] ?? false;

        if (!$ignore_featured) {
            $query['meta_query'] = array_merge($query['meta_query'] ?? [], [
                'is_featured_clause' => [
                    'key' => 'is_featured',
                    'compare' => 'EXISTS',
                ],
            ]);

            $query['orderby'] = array_merge([
                'is_featured_clause' => 'DESC',
                'date' => 'DESC'
            ], $query['orderby'] ?? []);
        }

        return new \WP_Query($query);
    }
}
