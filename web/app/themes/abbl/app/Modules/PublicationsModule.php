<?php

namespace App\Modules;

class PublicationsModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_PUBLICATION, [
            'menu_position' => 4,
            'has_archive' => true,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-media-document',
            'supports' => ['title', 'editor', 'thumbnail', 'excerpt'],
            'taxonomies' => [],
        ], [
            'slug' => 'publications',
        ]);

        register_extended_taxonomy(TAX_PUBLICATION_CATEGORY, CPT_PUBLICATION, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Publication categories',
            'singular' => 'Publication category',
        ]);

        register_extended_taxonomy(TAX_PUBLICATION_LANGUAGE, CPT_PUBLICATION, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Publication languages',
            'singular' => 'Publication language',
        ]);
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_PUBLICATION;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        $ignore_featured = $query['ignore_featured'] ?? false;

        if (!$ignore_featured) {
            $query['meta_query'] = array_merge($query['meta_query'] ?? [], [
                'is_featured_clause' => [
                    'key' => 'is_featured',
                    'compare' => 'EXISTS',
                ],
            ]);

            $query['orderby'] = array_merge([
                'is_featured_clause' => 'DESC',
                'date' => 'DESC'
            ], $query['orderby'] ?? []);
        }

        return new \WP_Query($query);
    }
}
