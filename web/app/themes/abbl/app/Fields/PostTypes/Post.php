<?php

namespace App\Fields\PostTypes;

use App\Fields\Partials\Components\BigNumber;
use App\Fields\Partials\Components\CenteredImage;
use App\Fields\Partials\Components\Quote;
use App\Fields\Partials\Components\RelatedNews;
use App\Fields\Partials\Components\RelatedPublications;
use App\Fields\Partials\Components\TextAndBigNumber;
use App\Fields\Partials\Components\TextColumns;
use App\Fields\Partials\Components\TextContent;
use App\Fields\Partials\Components\TextTwoColumns;
use App\Fields\Partials\FlexibleContent;
use App\Fields\Partials\Table;
use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Post extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('post');

        $fields
            ->setLocation('post_type', '==', 'post');

        $fields
            ->addTrueFalse('is_featured', [
                'label' => __('Featured', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addTrueFalse('has_takeaways', [
                'label' => __('Has takeaways', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addWysiwyg('takeaways_content', [
                'label' => __('Takeaways content', 'abbl-backend'),
            ])
                ->conditional('has_takeaways', '==', 1)
            ->addPartial(FlexibleContent::class);

        return $fields->build();
    }
}
