<?php

namespace App\Fields\Partials;

use App\Fields\Partials\Components\CenteredImage;
use App\Fields\Partials\Components\Quote;
use App\Fields\Partials\Components\RelatedNews;
use App\Fields\Partials\Components\RelatedPublications;
use App\Fields\Partials\Components\TextAndBigNumber;
use App\Fields\Partials\Components\TextColumns;
use App\Fields\Partials\Components\TextContent;
use App\Fields\Partials\Components\TextTwoColumns;
use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class FlexibleContent extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('flexible_content');

        $fields
            ->addFlexibleContent('sections')
                ->addLayout('text_columns_layout', [
                    'label' => __('Text columns', 'abbl-backend'),
                ])
                    ->addPartial(TextColumns::class)
                ->addLayout('text_two_columns_layout', [
                    'label' => __('Text two columns', 'abbl-backend'),
                ])
                    ->addPartial(TextTwoColumns::class)
                ->addLayout('centered_image_layout', [
                    'label' => __('Centered image', 'abbl-backend'),
                ])
                    ->addPartial(CenteredImage::class)
                ->addLayout('text_content_layout', [
                    'label' => __('Text content', 'abbl-backend'),
                ])
                ->addPartial(TextContent::class)
                ->addLayout('quote_layout', [
                    'label' => __('Quote', 'abbl-backend'),
                ])
                    ->addPartial(Quote::class)
                ->addLayout('related_publications_layout', [
                    'label' => __('Related publications', 'abbl-backend'),
                ])
                    ->addPartial(RelatedPublications::class)
                ->addLayout('related_news_layout', [
                    'label' => __('Related news', 'abbl-backend'),
                ])
                    ->addPartial(RelatedNews::class)
                ->addLayout('text_and_big_number_layout', [
                    'label' => __('Text and big number', 'abbl-backend'),
                ])
                    ->addPartial(TextAndBigNumber::class)
                ->addLayout('table_layout', [
                    'label' => __('Table', 'abbl-backend'),
                ])
                    ->addPartial(Table::class)
            ->endFlexibleContent();

        return $fields;
    }
}
