<?php

namespace App\Fields\Shared;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Hero extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('hero');

        $fields
            ->setLocation('post_type', '==', 'post')
            ->or('post_type', '==', 'page')
            ->or('post_type', '==', CPT_EVENT)
            ->or('post_type', '==', CPT_PUBLICATION);

        $fields
            ->addImage('hero_mobile_image', [
                'label' => __('Mobile image', 'abbl-backend'),
            ])
            ->addImage('hero_desktop_image', [
                'label' => __('Desktop image', 'abbl-backend'),
            ])
            ->addWysiwyg('hero_content', [
                'label' => __('Content', 'abbl-backend'),
                'instructions' => __("Optional content to display on top of the image. <br> By default, it's the post or page excerpt.", 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
