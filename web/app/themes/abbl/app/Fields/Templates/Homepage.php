<?php

namespace App\Fields\Templates;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Homepage extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('homepage');

        $fields
            ->setLocation('page_template', '==', 'template-homepage.blade.php');

        $fields
            ->addGroup('hero_section')
                ->addTextarea('title')
                ->addTextarea('subtitle')
            ->endGroup()
            ->addGroup('professional_section')
                ->setWidth(50)
                ->addText('title')
                ->addTextarea('description')
                ->addLink('button_link')
                ->addTextarea('detailed_description')
                ->addImage('image')
            ->endGroup()
            ->addGroup('consumer_section')
                ->setWidth(50)
                ->addText('title')
                ->addTextarea('description')
                ->addLink('button_link')
                ->addTextarea('detailed_description')
                ->addImage('image')
            ->endGroup()
            ->addGroup('bottom_message')
                ->addText('message')
            ->endGroup();

        return $fields->build();
    }
}
